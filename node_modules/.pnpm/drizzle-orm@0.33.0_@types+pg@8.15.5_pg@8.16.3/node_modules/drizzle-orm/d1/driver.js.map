{"version": 3, "sources": ["../../src/d1/driver.ts"], "sourcesContent": ["/// <reference types=\"@cloudflare/workers-types\" />\nimport type { BatchItem, BatchResponse } from '~/batch.ts';\nimport { entityKind } from '~/entity.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype ExtractTablesWithRelations,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { BaseSQLiteDatabase } from '~/sqlite-core/db.ts';\nimport { SQLiteAsyncDialect } from '~/sqlite-core/dialect.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport { SQLiteD1Session } from './session.ts';\n\nexport class DrizzleD1Database<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends BaseSQLiteDatabase<'async', D1Result, TSchema> {\n\tstatic readonly [entityKind]: string = 'D1Database';\n\n\t/** @internal */\n\tdeclare readonly session: SQLiteD1Session<TSchema, ExtractTablesWithRelations<TSchema>>;\n\n\tasync batch<U extends BatchItem<'sqlite'>, T extends Readonly<[U, ...U[]]>>(\n\t\tbatch: T,\n\t): Promise<BatchResponse<T>> {\n\t\treturn this.session.batch(batch) as Promise<BatchResponse<T>>;\n\t}\n}\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: D1Database,\n\tconfig: DrizzleConfig<TSchema> = {},\n): DrizzleD1Database<TSchema> {\n\tconst dialect = new SQLiteAsyncDialect();\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new SQLiteD1Session(client, dialect, schema, { logger });\n\treturn new DrizzleD1Database('async', dialect, session, schema) as DrizzleD1Database<TSchema>;\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAC3B,SAAS,qBAAqB;AAC9B;AAAA,EACC;AAAA,EACA;AAAA,OAIM;AACP,SAAS,0BAA0B;AACnC,SAAS,0BAA0B;AAEnC,SAAS,uBAAuB;AAEzB,MAAM,0BAEH,mBAA+C;AAAA,EACxD,QAAiB,UAAU,IAAY;AAAA,EAKvC,MAAM,MACL,OAC4B;AAC5B,WAAO,KAAK,QAAQ,MAAM,KAAK;AAAA,EAChC;AACD;AAEO,SAAS,QACf,QACA,SAAiC,CAAC,GACL;AAC7B,QAAM,UAAU,IAAI,mBAAmB;AACvC,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,eAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,gBAAgB,QAAQ,SAAS,QAAQ,EAAE,OAAO,CAAC;AACvE,SAAO,IAAI,kBAAkB,SAAS,SAAS,SAAS,MAAM;AAC/D;", "names": []}