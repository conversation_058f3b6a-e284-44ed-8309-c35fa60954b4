{"version": 3, "sources": ["../../../src/aws-data-api/pg/driver.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport type { SQL, SQLWrapper } from '~/index.ts';\nimport { Param, sql, Table } from '~/index.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { PgDatabase } from '~/pg-core/db.ts';\nimport { PgDialect } from '~/pg-core/dialect.ts';\nimport type { PgColumn, PgInsertConfig, PgTable, TableConfig } from '~/pg-core/index.ts';\nimport { PgArray } from '~/pg-core/index.ts';\nimport type { PgRaw } from '~/pg-core/query-builders/raw.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport type { DrizzleConfig, UpdateSet } from '~/utils.ts';\nimport type { AwsDataApiClient, AwsDataApiPgQueryResult, AwsDataApiPgQueryResultHKT } from './session.ts';\nimport { AwsDataApiSession } from './session.ts';\n\nexport interface PgDriverOptions {\n\tlogger?: Logger;\n\tdatabase: string;\n\tresourceArn: string;\n\tsecretArn: string;\n}\n\nexport interface DrizzleAwsDataApiPgConfig<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends DrizzleConfig<TSchema> {\n\tdatabase: string;\n\tresourceArn: string;\n\tsecretArn: string;\n}\n\nexport class AwsDataApiPgDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends PgDatabase<AwsDataApiPgQueryResultHKT, TSchema> {\n\tstatic readonly [entityKind]: string = 'AwsDataApiPgDatabase';\n\n\toverride execute<\n\t\tTRow extends Record<string, unknown> = Record<string, unknown>,\n\t>(query: SQLWrapper): PgRaw<AwsDataApiPgQueryResult<TRow>> {\n\t\treturn super.execute(query);\n\t}\n}\n\nexport class AwsPgDialect extends PgDialect {\n\tstatic readonly [entityKind]: string = 'AwsPgDialect';\n\n\toverride escapeParam(num: number): string {\n\t\treturn `:${num + 1}`;\n\t}\n\n\toverride buildInsertQuery(\n\t\t{ table, values, onConflict, returning }: PgInsertConfig<PgTable<TableConfig>>,\n\t): SQL<unknown> {\n\t\tconst columns: Record<string, PgColumn> = table[Table.Symbol.Columns];\n\t\tfor (const value of values) {\n\t\t\tfor (const fieldName of Object.keys(columns)) {\n\t\t\t\tconst colValue = value[fieldName];\n\t\t\t\tif (\n\t\t\t\t\tis(colValue, Param) && colValue.value !== undefined && is(colValue.encoder, PgArray)\n\t\t\t\t\t&& Array.isArray(colValue.value)\n\t\t\t\t) {\n\t\t\t\t\tvalue[fieldName] = sql`cast(${colValue} as ${sql.raw(colValue.encoder.getSQLType())})`;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn super.buildInsertQuery({ table, values, onConflict, returning });\n\t}\n\n\toverride buildUpdateSet(table: PgTable<TableConfig>, set: UpdateSet): SQL<unknown> {\n\t\tconst columns: Record<string, PgColumn> = table[Table.Symbol.Columns];\n\n\t\tfor (const [colName, colValue] of Object.entries(set)) {\n\t\t\tconst currentColumn = columns[colName];\n\t\t\tif (\n\t\t\t\tcurrentColumn && is(colValue, Param) && colValue.value !== undefined && is(colValue.encoder, PgArray)\n\t\t\t\t&& Array.isArray(colValue.value)\n\t\t\t) {\n\t\t\t\tset[colName] = sql`cast(${colValue} as ${sql.raw(colValue.encoder.getSQLType())})`;\n\t\t\t}\n\t\t}\n\t\treturn super.buildUpdateSet(table, set);\n\t}\n}\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: AwsDataApiClient,\n\tconfig: DrizzleAwsDataApiPgConfig<TSchema>,\n): AwsDataApiPgDatabase<TSchema> {\n\tconst dialect = new AwsPgDialect();\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new AwsDataApiSession(client, dialect, schema, { ...config, logger }, undefined);\n\treturn new PgDatabase(dialect, session, schema) as AwsDataApiPgDatabase<TSchema>;\n}\n"], "mappings": "AAAA,SAAS,YAAY,UAAU;AAE/B,SAAS,OAAO,KAAK,aAAa;AAElC,SAAS,qBAAqB;AAC9B,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;AAE1B,SAAS,eAAe;AAExB;AAAA,EACC;AAAA,EACA;AAAA,OAGM;AAGP,SAAS,yBAAyB;AAiB3B,MAAM,6BAEH,WAAgD;AAAA,EACzD,QAAiB,UAAU,IAAY;AAAA,EAE9B,QAEP,OAAyD;AAC1D,WAAO,MAAM,QAAQ,KAAK;AAAA,EAC3B;AACD;AAEO,MAAM,qBAAqB,UAAU;AAAA,EAC3C,QAAiB,UAAU,IAAY;AAAA,EAE9B,YAAY,KAAqB;AACzC,WAAO,IAAI,MAAM,CAAC;AAAA,EACnB;AAAA,EAES,iBACR,EAAE,OAAO,QAAQ,YAAY,UAAU,GACxB;AACf,UAAM,UAAoC,MAAM,MAAM,OAAO,OAAO;AACpE,eAAW,SAAS,QAAQ;AAC3B,iBAAW,aAAa,OAAO,KAAK,OAAO,GAAG;AAC7C,cAAM,WAAW,MAAM,SAAS;AAChC,YACC,GAAG,UAAU,KAAK,KAAK,SAAS,UAAU,UAAa,GAAG,SAAS,SAAS,OAAO,KAChF,MAAM,QAAQ,SAAS,KAAK,GAC9B;AACD,gBAAM,SAAS,IAAI,WAAW,QAAQ,OAAO,IAAI,IAAI,SAAS,QAAQ,WAAW,CAAC,CAAC;AAAA,QACpF;AAAA,MACD;AAAA,IACD;AAEA,WAAO,MAAM,iBAAiB,EAAE,OAAO,QAAQ,YAAY,UAAU,CAAC;AAAA,EACvE;AAAA,EAES,eAAe,OAA6B,KAA8B;AAClF,UAAM,UAAoC,MAAM,MAAM,OAAO,OAAO;AAEpE,eAAW,CAAC,SAAS,QAAQ,KAAK,OAAO,QAAQ,GAAG,GAAG;AACtD,YAAM,gBAAgB,QAAQ,OAAO;AACrC,UACC,iBAAiB,GAAG,UAAU,KAAK,KAAK,SAAS,UAAU,UAAa,GAAG,SAAS,SAAS,OAAO,KACjG,MAAM,QAAQ,SAAS,KAAK,GAC9B;AACD,YAAI,OAAO,IAAI,WAAW,QAAQ,OAAO,IAAI,IAAI,SAAS,QAAQ,WAAW,CAAC,CAAC;AAAA,MAChF;AAAA,IACD;AACA,WAAO,MAAM,eAAe,OAAO,GAAG;AAAA,EACvC;AACD;AAEO,SAAS,QACf,QACA,QACgC;AAChC,QAAM,UAAU,IAAI,aAAa;AACjC,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,eAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,kBAAkB,QAAQ,SAAS,QAAQ,EAAE,GAAG,QAAQ,OAAO,GAAG,MAAS;AAC/F,SAAO,IAAI,WAAW,SAAS,SAAS,MAAM;AAC/C;", "names": []}