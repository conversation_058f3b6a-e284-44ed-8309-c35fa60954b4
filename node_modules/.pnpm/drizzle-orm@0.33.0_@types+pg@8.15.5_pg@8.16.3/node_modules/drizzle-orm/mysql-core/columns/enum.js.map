{"version": 3, "sources": ["../../../src/mysql-core/columns/enum.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport type { Writable } from '~/utils.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport type MySqlEnumColumnBuilderInitial<TName extends string, TEnum extends [string, ...string[]]> =\n\tMySqlEnumColumnBuilder<{\n\t\tname: TName;\n\t\tdataType: 'string';\n\t\tcolumnType: 'MySqlEnumColumn';\n\t\tdata: TEnum[number];\n\t\tdriverParam: string;\n\t\tenumValues: TEnum;\n\t\tgenerated: undefined;\n\t}>;\n\nexport class MySqlEnumColumnBuilder<T extends ColumnBuilderBaseConfig<'string', 'MySqlEnumColumn'>>\n\textends MySqlColumnBuilder<T, { enumValues: T['enumValues'] }>\n{\n\tstatic readonly [entityKind]: string = 'MySqlEnumColumnBuilder';\n\n\tconstructor(name: T['name'], values: T['enumValues']) {\n\t\tsuper(name, 'string', 'MySqlEnumColumn');\n\t\tthis.config.enumValues = values;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlEnumColumn<MakeColumnConfig<T, TTableName> & { enumValues: T['enumValues'] }> {\n\t\treturn new MySqlEnumColumn<MakeColumnConfig<T, TTableName> & { enumValues: T['enumValues'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlEnumColumn<T extends ColumnBaseConfig<'string', 'MySqlEnumColumn'>>\n\textends MySqlColumn<T, { enumValues: T['enumValues'] }>\n{\n\tstatic readonly [entityKind]: string = 'MySqlEnumColumn';\n\n\toverride readonly enumValues = this.config.enumValues;\n\n\tgetSQLType(): string {\n\t\treturn `enum(${this.enumValues!.map((value) => `'${value}'`).join(',')})`;\n\t}\n}\n\nexport function mysqlEnum<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tvalues: T | Writable<T>,\n): MySqlEnumColumnBuilderInitial<TName, Writable<T>> {\n\tif (values.length === 0) {\n\t\tthrow new Error(`You have an empty array for \"${name}\" enum values`);\n\t}\n\n\treturn new MySqlEnumColumnBuilder(name, values);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAG3B,SAAS,aAAa,0BAA0B;AAazC,MAAM,+BACJ,mBACT;AAAA,EACC,QAAiB,UAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB,QAAyB;AACrD,UAAM,MAAM,UAAU,iBAAiB;AACvC,SAAK,OAAO,aAAa;AAAA,EAC1B;AAAA;AAAA,EAGS,MACR,OACqF;AACrF,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,wBACJ,YACT;AAAA,EACC,QAAiB,UAAU,IAAY;AAAA,EAErB,aAAa,KAAK,OAAO;AAAA,EAE3C,aAAqB;AACpB,WAAO,QAAQ,KAAK,WAAY,IAAI,CAAC,UAAU,IAAI,KAAK,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,EACvE;AACD;AAEO,SAAS,UACf,MACA,QACoD;AACpD,MAAI,OAAO,WAAW,GAAG;AACxB,UAAM,IAAI,MAAM,gCAAgC,IAAI,eAAe;AAAA,EACpE;AAEA,SAAO,IAAI,uBAAuB,MAAM,MAAM;AAC/C;", "names": []}