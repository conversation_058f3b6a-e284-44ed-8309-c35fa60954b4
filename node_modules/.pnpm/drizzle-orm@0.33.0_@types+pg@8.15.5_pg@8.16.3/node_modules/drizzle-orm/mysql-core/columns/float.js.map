{"version": 3, "sources": ["../../../src/mysql-core/columns/float.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { MySqlColumnBuilderWithAutoIncrement, MySqlColumnWithAutoIncrement } from './common.ts';\n\nexport type MySqlFloatBuilderInitial<TName extends string> = MySqlFloatBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'MySqlFloat';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class MySqlFloatBuilder<T extends ColumnBuilderBaseConfig<'number', 'MySqlFloat'>>\n\textends MySqlColumnBuilderWithAutoIncrement<T>\n{\n\tstatic readonly [entityKind]: string = 'MySqlFloatBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'MySqlFloat');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlFloat<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlFloat<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class MySqlFloat<T extends ColumnBaseConfig<'number', 'MySqlFloat'>> extends MySqlColumnWithAutoIncrement<T> {\n\tstatic readonly [entityKind]: string = 'MySqlFloat';\n\n\tgetSQLType(): string {\n\t\treturn 'float';\n\t}\n}\n\nexport function float<TName extends string>(name: TName): MySqlFloatBuilderInitial<TName> {\n\treturn new MySqlFloatBuilder(name);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,qCAAqC,oCAAoC;AAY3E,MAAM,0BACJ,oCACT;AAAA,EACC,QAAiB,UAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,YAAY;AAAA,EACnC;AAAA;AAAA,EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI,WAA4C,OAAO,KAAK,MAA8C;AAAA,EAClH;AACD;AAEO,MAAM,mBAAuE,6BAAgC;AAAA,EACnH,QAAiB,UAAU,IAAY;AAAA,EAEvC,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAEO,SAAS,MAA4B,MAA8C;AACzF,SAAO,IAAI,kBAAkB,IAAI;AAClC;", "names": []}