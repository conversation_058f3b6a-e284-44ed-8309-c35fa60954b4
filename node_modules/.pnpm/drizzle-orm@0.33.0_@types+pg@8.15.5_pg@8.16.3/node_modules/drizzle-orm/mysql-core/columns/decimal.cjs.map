{"version": 3, "sources": ["../../../src/mysql-core/columns/decimal.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { MySqlColumnBuilderWithAutoIncrement, MySqlColumnWithAutoIncrement } from './common.ts';\n\nexport type MySqlDecimalBuilderInitial<TName extends string> = MySqlDecimalBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'MySqlDecimal';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class MySqlDecimalBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'MySqlDecimal'>,\n> extends MySqlColumnBuilderWithAutoIncrement<T, MySqlDecimalConfig> {\n\tstatic readonly [entityKind]: string = 'MySqlDecimalBuilder';\n\n\tconstructor(name: T['name'], precision?: number, scale?: number) {\n\t\tsuper(name, 'string', 'MySqlDecimal');\n\t\tthis.config.precision = precision;\n\t\tthis.config.scale = scale;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlDecimal<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlDecimal<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlDecimal<T extends ColumnBaseConfig<'string', 'MySqlDecimal'>>\n\textends MySqlColumnWithAutoIncrement<T, MySqlDecimalConfig>\n{\n\tstatic readonly [entityKind]: string = 'MySqlDecimal';\n\n\treadonly precision: number | undefined = this.config.precision;\n\treadonly scale: number | undefined = this.config.scale;\n\n\tgetSQLType(): string {\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\treturn `decimal(${this.precision},${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\treturn 'decimal';\n\t\t} else {\n\t\t\treturn `decimal(${this.precision})`;\n\t\t}\n\t}\n}\n\nexport interface MySqlDecimalConfig {\n\tprecision?: number;\n\tscale?: number;\n}\n\nexport function decimal<TName extends string>(\n\tname: TName,\n\tconfig: MySqlDecimalConfig = {},\n): MySqlDecimalBuilderInitial<TName> {\n\treturn new MySqlDecimalBuilder(name, config.precision, config.scale);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAAkF;AAY3E,MAAM,4BAEH,kDAA2D;AAAA,EACpE,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB,WAAoB,OAAgB;AAChE,UAAM,MAAM,UAAU,cAAc;AACpC,SAAK,OAAO,YAAY;AACxB,SAAK,OAAO,QAAQ;AAAA,EACrB;AAAA;AAAA,EAGS,MACR,OACgD;AAChD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,qBACJ,2CACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAE9B,YAAgC,KAAK,OAAO;AAAA,EAC5C,QAA4B,KAAK,OAAO;AAAA,EAEjD,aAAqB;AACpB,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,aAAO,WAAW,KAAK,SAAS,IAAI,KAAK,KAAK;AAAA,IAC/C,WAAW,KAAK,cAAc,QAAW;AACxC,aAAO;AAAA,IACR,OAAO;AACN,aAAO,WAAW,KAAK,SAAS;AAAA,IACjC;AAAA,EACD;AACD;AAOO,SAAS,QACf,MACA,SAA6B,CAAC,GACM;AACpC,SAAO,IAAI,oBAAoB,MAAM,OAAO,WAAW,OAAO,KAAK;AACpE;", "names": []}