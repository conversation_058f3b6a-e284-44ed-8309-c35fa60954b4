{"version": 3, "sources": ["../../../src/mysql-core/columns/time.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport type MySqlTimeBuilderInitial<TName extends string> = MySqlTimeBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'MySqlTime';\n\tdata: string;\n\tdriverParam: string | number;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class MySqlTimeBuilder<T extends ColumnBuilderBaseConfig<'string', 'MySqlTime'>> extends MySqlColumnBuilder<\n\tT,\n\tTimeConfig\n> {\n\tstatic readonly [entityKind]: string = 'MySqlTimeBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t\tconfig: TimeConfig | undefined,\n\t) {\n\t\tsuper(name, 'string', 'MySqlTime');\n\t\tthis.config.fsp = config?.fsp;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlTime<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlTime<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class MySqlTime<\n\tT extends ColumnBaseConfig<'string', 'MySqlTime'>,\n> extends MySqlColumn<T, TimeConfig> {\n\tstatic readonly [entityKind]: string = 'MySqlTime';\n\n\treadonly fsp: number | undefined = this.config.fsp;\n\n\tgetSQLType(): string {\n\t\tconst precision = this.fsp === undefined ? '' : `(${this.fsp})`;\n\t\treturn `time${precision}`;\n\t}\n}\n\nexport type TimeConfig = {\n\tfsp?: 0 | 1 | 2 | 3 | 4 | 5 | 6;\n};\n\nexport function time<TName extends string>(name: TName, config?: TimeConfig): MySqlTimeBuilderInitial<TName> {\n\treturn new MySqlTimeBuilder(name, config);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,aAAa,0BAA0B;AAYzC,MAAM,yBAAmF,mBAG9F;AAAA,EACD,QAAiB,UAAU,IAAY;AAAA,EAEvC,YACC,MACA,QACC;AACD,UAAM,MAAM,UAAU,WAAW;AACjC,SAAK,OAAO,MAAM,QAAQ;AAAA,EAC3B;AAAA;AAAA,EAGS,MACR,OAC6C;AAC7C,WAAO,IAAI,UAA2C,OAAO,KAAK,MAA8C;AAAA,EACjH;AACD;AAEO,MAAM,kBAEH,YAA2B;AAAA,EACpC,QAAiB,UAAU,IAAY;AAAA,EAE9B,MAA0B,KAAK,OAAO;AAAA,EAE/C,aAAqB;AACpB,UAAM,YAAY,KAAK,QAAQ,SAAY,KAAK,IAAI,KAAK,GAAG;AAC5D,WAAO,OAAO,SAAS;AAAA,EACxB;AACD;AAMO,SAAS,KAA2B,MAAa,QAAqD;AAC5G,SAAO,IAAI,iBAAiB,MAAM,MAAM;AACzC;", "names": []}