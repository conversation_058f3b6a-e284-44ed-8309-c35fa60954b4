{"version": 3, "sources": ["../../../src/mysql-core/columns/binary.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { MySqlColumn, MySqlColumnBuilder } from './common.ts';\n\nexport type MySqlBinaryBuilderInitial<TName extends string> = MySqlBinaryBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'MySqlBinary';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class MySqlBinaryBuilder<T extends ColumnBuilderBaseConfig<'string', 'MySqlBinary'>> extends MySqlColumnBuilder<\n\tT,\n\tMySqlBinaryConfig\n> {\n\tstatic readonly [entityKind]: string = 'MySqlBinaryBuilder';\n\n\tconstructor(name: T['name'], length: number | undefined) {\n\t\tsuper(name, 'string', 'MySqlBinary');\n\t\tthis.config.length = length;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlBinary<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlBinary<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class MySqlBinary<T extends ColumnBaseConfig<'string', 'MySqlBinary'>> extends MySqlColumn<\n\tT,\n\tMySqlBinaryConfig\n> {\n\tstatic readonly [entityKind]: string = 'MySqlBinary';\n\n\tlength: number | undefined = this.config.length;\n\n\tgetSQLType(): string {\n\t\treturn this.length === undefined ? `binary` : `binary(${this.length})`;\n\t}\n}\n\nexport interface MySqlBinaryConfig {\n\tlength?: number;\n}\n\nexport function binary<TName extends string>(\n\tname: TName,\n\tconfig: MySqlBinaryConfig = {},\n): MySqlBinaryBuilderInitial<TName> {\n\treturn new MySqlBinaryBuilder(name, config.length);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAAgD;AAYzC,MAAM,2BAAuF,iCAGlG;AAAA,EACD,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB,QAA4B;AACxD,UAAM,MAAM,UAAU,aAAa;AACnC,SAAK,OAAO,SAAS;AAAA,EACtB;AAAA;AAAA,EAGS,MACR,OAC+C;AAC/C,WAAO,IAAI,YAA6C,OAAO,KAAK,MAA8C;AAAA,EACnH;AACD;AAEO,MAAM,oBAAyE,0BAGpF;AAAA,EACD,QAAiB,wBAAU,IAAY;AAAA,EAEvC,SAA6B,KAAK,OAAO;AAAA,EAEzC,aAAqB;AACpB,WAAO,KAAK,WAAW,SAAY,WAAW,UAAU,KAAK,MAAM;AAAA,EACpE;AACD;AAMO,SAAS,OACf,MACA,SAA4B,CAAC,GACM;AACnC,SAAO,IAAI,mBAAmB,MAAM,OAAO,MAAM;AAClD;", "names": []}