{"version": 3, "sources": ["../../../src/mysql-core/columns/tinyint.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { MySqlColumnBuilderWithAutoIncrement, MySqlColumnWithAutoIncrement } from './common.ts';\nimport type { MySqlIntConfig } from './int.ts';\n\nexport type MySqlTinyIntBuilderInitial<TName extends string> = MySqlTinyIntBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'MySqlTinyInt';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class MySqlTinyIntBuilder<T extends ColumnBuilderBaseConfig<'number', 'MySqlTinyInt'>>\n\textends MySqlColumnBuilderWithAutoIncrement<T, MySqlIntConfig>\n{\n\tstatic readonly [entityKind]: string = 'MySqlTinyIntBuilder';\n\n\tconstructor(name: T['name'], config?: MySqlIntConfig) {\n\t\tsuper(name, 'number', 'MySqlTinyInt');\n\t\tthis.config.unsigned = config ? config.unsigned : false;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlTinyInt<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlTinyInt<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class MySqlTinyInt<T extends ColumnBaseConfig<'number', 'MySqlTinyInt'>>\n\textends MySqlColumnWithAutoIncrement<T, MySqlIntConfig>\n{\n\tstatic readonly [entityKind]: string = 'MySqlTinyInt';\n\n\tgetSQLType(): string {\n\t\treturn `tinyint${this.config.unsigned ? ' unsigned' : ''}`;\n\t}\n\n\toverride mapFromDriverValue(value: number | string): number {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number(value);\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function tinyint<TName extends string>(name: TName, config?: MySqlIntConfig): MySqlTinyIntBuilderInitial<TName> {\n\treturn new MySqlTinyIntBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAAkF;AAa3E,MAAM,4BACJ,kDACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB,QAAyB;AACrD,UAAM,MAAM,UAAU,cAAc;AACpC,SAAK,OAAO,WAAW,SAAS,OAAO,WAAW;AAAA,EACnD;AAAA;AAAA,EAGS,MACR,OACgD;AAChD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,qBACJ,2CACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,aAAqB;AACpB,WAAO,UAAU,KAAK,OAAO,WAAW,cAAc,EAAE;AAAA,EACzD;AAAA,EAES,mBAAmB,OAAgC;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,OAAO,KAAK;AAAA,IACpB;AACA,WAAO;AAAA,EACR;AACD;AAEO,SAAS,QAA8B,MAAa,QAA4D;AACtH,SAAO,IAAI,oBAAoB,MAAM,MAAM;AAC5C;", "names": []}