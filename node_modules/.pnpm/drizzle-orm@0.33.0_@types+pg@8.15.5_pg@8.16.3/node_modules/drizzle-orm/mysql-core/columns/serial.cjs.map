{"version": 3, "sources": ["../../../src/mysql-core/columns/serial.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBaseConfig,\n\tColumnBuilder<PERSON>untime<PERSON>onfig,\n\tHasDefault,\n\tIsAutoincrement,\n\tIsP<PERSON>ry<PERSON>ey,\n\tMakeColumnConfig,\n\tNotNull,\n} from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { MySqlColumnBuilderWithAutoIncrement, MySqlColumnWithAutoIncrement } from './common.ts';\n\nexport type MySqlSerialBuilderInitial<TName extends string> = IsAutoincrement<\n\tIsPrimaryKey<\n\t\tNotNull<\n\t\t\tHasDefault<\n\t\t\t\tMySqlSerialBuilder<{\n\t\t\t\t\tname: TName;\n\t\t\t\t\tdataType: 'number';\n\t\t\t\t\tcolumnType: 'MySqlSerial';\n\t\t\t\t\tdata: number;\n\t\t\t\t\tdriverParam: number;\n\t\t\t\t\tenumValues: undefined;\n\t\t\t\t\tgenerated: undefined;\n\t\t\t\t}>\n\t\t\t>\n\t\t>\n\t>\n>;\n\nexport class MySqlSerialBuilder<T extends ColumnBuilderBaseConfig<'number', 'MySqlSerial'>>\n\textends MySqlColumnBuilderWithAutoIncrement<T>\n{\n\tstatic readonly [entityKind]: string = 'MySqlSerialBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'MySqlSerial');\n\t\tthis.config.hasDefault = true;\n\t\tthis.config.autoIncrement = true;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlSerial<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlSerial<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class MySqlSerial<\n\tT extends ColumnBaseConfig<'number', 'MySqlSerial'>,\n> extends MySqlColumnWithAutoIncrement<T> {\n\tstatic readonly [entityKind]: string = 'MySqlSerial';\n\n\tgetSQLType(): string {\n\t\treturn 'serial';\n\t}\n\n\toverride mapFromDriverValue(value: number | string): number {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number(value);\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function serial<TName extends string>(name: TName): MySqlSerialBuilderInitial<TName> {\n\treturn new MySqlSerialBuilder(name) as MySqlSerialBuilderInitial<TName>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,oBAA2B;AAE3B,oBAAkF;AAoB3E,MAAM,2BACJ,kDACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,aAAa;AACnC,SAAK,OAAO,aAAa;AACzB,SAAK,OAAO,gBAAgB;AAAA,EAC7B;AAAA;AAAA,EAGS,MACR,OAC+C;AAC/C,WAAO,IAAI,YAA6C,OAAO,KAAK,MAA8C;AAAA,EACnH;AACD;AAEO,MAAM,oBAEH,2CAAgC;AAAA,EACzC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAgC;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,OAAO,KAAK;AAAA,IACpB;AACA,WAAO;AAAA,EACR;AACD;AAEO,SAAS,OAA6B,MAA+C;AAC3F,SAAO,IAAI,mBAAmB,IAAI;AACnC;", "names": []}