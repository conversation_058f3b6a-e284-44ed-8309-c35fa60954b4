{"version": 3, "sources": ["../../../src/mysql-core/columns/double.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyMySqlTable } from '~/mysql-core/table.ts';\nimport { MySqlColumnBuilderWithAutoIncrement, MySqlColumnWithAutoIncrement } from './common.ts';\n\nexport type MySqlDoubleBuilderInitial<TName extends string> = MySqlDoubleBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'MySqlDouble';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class MySqlDoubleBuilder<T extends ColumnBuilderBaseConfig<'number', 'MySqlDouble'>>\n\textends MySqlColumnBuilderWithAutoIncrement<T, MySqlDoubleConfig>\n{\n\tstatic readonly [entityKind]: string = 'MySqlDoubleBuilder';\n\n\tconstructor(name: T['name'], config: MySqlDoubleConfig | undefined) {\n\t\tsuper(name, 'number', 'MySqlDouble');\n\t\tthis.config.precision = config?.precision;\n\t\tthis.config.scale = config?.scale;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyMySqlTable<{ name: TTableName }>,\n\t): MySqlDouble<MakeColumnConfig<T, TTableName>> {\n\t\treturn new MySqlDouble<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class MySqlDouble<T extends ColumnBaseConfig<'number', 'MySqlDouble'>>\n\textends MySqlColumnWithAutoIncrement<T, MySqlDoubleConfig>\n{\n\tstatic readonly [entityKind]: string = 'MySqlDouble';\n\n\tprecision: number | undefined = this.config.precision;\n\tscale: number | undefined = this.config.scale;\n\n\tgetSQLType(): string {\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\treturn `double(${this.precision},${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\treturn 'double';\n\t\t} else {\n\t\t\treturn `double(${this.precision})`;\n\t\t}\n\t}\n}\n\nexport interface MySqlDoubleConfig {\n\tprecision?: number;\n\tscale?: number;\n}\n\nexport function double<TName extends string>(\n\tname: TName,\n\tconfig?: MySqlDoubleConfig,\n): MySqlDoubleBuilderInitial<TName> {\n\treturn new MySqlDoubleBuilder(name, config);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,qCAAqC,oCAAoC;AAY3E,MAAM,2BACJ,oCACT;AAAA,EACC,QAAiB,UAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB,QAAuC;AACnE,UAAM,MAAM,UAAU,aAAa;AACnC,SAAK,OAAO,YAAY,QAAQ;AAChC,SAAK,OAAO,QAAQ,QAAQ;AAAA,EAC7B;AAAA;AAAA,EAGS,MACR,OAC+C;AAC/C,WAAO,IAAI,YAA6C,OAAO,KAAK,MAA8C;AAAA,EACnH;AACD;AAEO,MAAM,oBACJ,6BACT;AAAA,EACC,QAAiB,UAAU,IAAY;AAAA,EAEvC,YAAgC,KAAK,OAAO;AAAA,EAC5C,QAA4B,KAAK,OAAO;AAAA,EAExC,aAAqB;AACpB,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,aAAO,UAAU,KAAK,SAAS,IAAI,KAAK,KAAK;AAAA,IAC9C,WAAW,KAAK,cAAc,QAAW;AACxC,aAAO;AAAA,IACR,OAAO;AACN,aAAO,UAAU,KAAK,SAAS;AAAA,IAChC;AAAA,EACD;AACD;AAOO,SAAS,OACf,MACA,QACmC;AACnC,SAAO,IAAI,mBAAmB,MAAM,MAAM;AAC3C;", "names": []}