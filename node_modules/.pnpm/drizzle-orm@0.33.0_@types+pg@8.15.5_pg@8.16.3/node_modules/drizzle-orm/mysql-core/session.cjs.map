{"version": 3, "sources": ["../../src/mysql-core/session.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { TransactionRollbackError } from '~/errors.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { type Query, type SQL, sql } from '~/sql/sql.ts';\nimport type { Assume, Equal } from '~/utils.ts';\nimport { MySqlDatabase } from './db.ts';\nimport type { MySqlDialect } from './dialect.ts';\nimport type { SelectedFieldsOrdered } from './query-builders/select.types.ts';\n\nexport type Mode = 'default' | 'planetscale';\n\nexport interface MySqlQueryResultHKT {\n\treadonly $brand: 'MySqlQueryResultHKT';\n\treadonly row: unknown;\n\treadonly type: unknown;\n}\n\nexport interface AnyMySqlQueryResultHKT extends MySqlQueryResultHKT {\n\treadonly type: any;\n}\n\nexport type MySqlQueryResultKind<TKind extends MySqlQueryResultHKT, TRow> = (TKind & {\n\treadonly row: TRow;\n})['type'];\n\nexport interface MySqlPreparedQueryConfig {\n\texecute: unknown;\n\titerator: unknown;\n}\n\nexport interface MySqlPreparedQueryHKT {\n\treadonly $brand: 'MySqlPreparedQueryHKT';\n\treadonly config: unknown;\n\treadonly type: unknown;\n}\n\nexport type PreparedQueryKind<\n\tTKind extends MySqlPreparedQueryHKT,\n\tTConfig extends MySqlPreparedQueryConfig,\n\tTAssume extends boolean = false,\n> = Equal<TAssume, true> extends true\n\t? Assume<(TKind & { readonly config: TConfig })['type'], MySqlPreparedQuery<TConfig>>\n\t: (TKind & { readonly config: TConfig })['type'];\n\nexport abstract class MySqlPreparedQuery<T extends MySqlPreparedQueryConfig> {\n\tstatic readonly [entityKind]: string = 'MySqlPreparedQuery';\n\n\t/** @internal */\n\tjoinsNotNullableMap?: Record<string, boolean>;\n\n\tabstract execute(placeholderValues?: Record<string, unknown>): Promise<T['execute']>;\n\n\tabstract iterator(placeholderValues?: Record<string, unknown>): AsyncGenerator<T['iterator']>;\n}\n\nexport interface MySqlTransactionConfig {\n\twithConsistentSnapshot?: boolean;\n\taccessMode?: 'read only' | 'read write';\n\tisolationLevel: 'read uncommitted' | 'read committed' | 'repeatable read' | 'serializable';\n}\n\nexport abstract class MySqlSession<\n\tTQueryResult extends MySqlQueryResultHKT = MySqlQueryResultHKT,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase = PreparedQueryHKTBase,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = Record<string, never>,\n> {\n\tstatic readonly [entityKind]: string = 'MySqlSession';\n\n\tconstructor(protected dialect: MySqlDialect) {}\n\n\tabstract prepareQuery<T extends MySqlPreparedQueryConfig, TPreparedQueryHKT extends MySqlPreparedQueryHKT>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\tgeneratedIds?: Record<string, unknown>[],\n\t\treturningIds?: SelectedFieldsOrdered,\n\t): PreparedQueryKind<TPreparedQueryHKT, T>;\n\n\texecute<T>(query: SQL): Promise<T> {\n\t\treturn this.prepareQuery<MySqlPreparedQueryConfig & { execute: T }, PreparedQueryHKTBase>(\n\t\t\tthis.dialect.sqlToQuery(query),\n\t\t\tundefined,\n\t\t).execute();\n\t}\n\n\tabstract all<T = unknown>(query: SQL): Promise<T[]>;\n\n\tabstract transaction<T>(\n\t\ttransaction: (tx: MySqlTransaction<TQueryResult, TPreparedQueryHKT, TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: MySqlTransactionConfig,\n\t): Promise<T>;\n\n\tprotected getSetTransactionSQL(config: MySqlTransactionConfig): SQL | undefined {\n\t\tconst parts: string[] = [];\n\n\t\tif (config.isolationLevel) {\n\t\t\tparts.push(`isolation level ${config.isolationLevel}`);\n\t\t}\n\n\t\treturn parts.length ? sql`set transaction ${sql.raw(parts.join(' '))}` : undefined;\n\t}\n\n\tprotected getStartTransactionSQL(config: MySqlTransactionConfig): SQL | undefined {\n\t\tconst parts: string[] = [];\n\n\t\tif (config.withConsistentSnapshot) {\n\t\t\tparts.push('with consistent snapshot');\n\t\t}\n\n\t\tif (config.accessMode) {\n\t\t\tparts.push(config.accessMode);\n\t\t}\n\n\t\treturn parts.length ? sql`start transaction ${sql.raw(parts.join(' '))}` : undefined;\n\t}\n}\n\nexport abstract class MySqlTransaction<\n\tTQueryResult extends MySqlQueryResultHKT,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = Record<string, never>,\n> extends MySqlDatabase<TQueryResult, TPreparedQueryHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'MySqlTransaction';\n\n\tconstructor(\n\t\tdialect: MySqlDialect,\n\t\tsession: MySqlSession,\n\t\tprotected schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprotected readonly nestedIndex: number,\n\t\tmode: Mode,\n\t) {\n\t\tsuper(dialect, session, schema, mode);\n\t}\n\n\trollback(): never {\n\t\tthrow new TransactionRollbackError();\n\t}\n\n\t/** Nested transactions (aka savepoints) only work with InnoDB engine. */\n\tabstract override transaction<T>(\n\t\ttransaction: (tx: MySqlTransaction<TQueryResult, TPreparedQueryHKT, TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T>;\n}\n\nexport interface PreparedQueryHKTBase extends MySqlPreparedQueryHKT {\n\ttype: MySqlPreparedQuery<Assume<this['config'], MySqlPreparedQueryConfig>>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAC3B,oBAAyC;AAEzC,iBAA0C;AAE1C,gBAA8B;AAuCvB,MAAe,mBAAuD;AAAA,EAC5E,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAGvC;AAKD;AAQO,MAAe,aAKpB;AAAA,EAGD,YAAsB,SAAuB;AAAvB;AAAA,EAAwB;AAAA,EAF9C,QAAiB,wBAAU,IAAY;AAAA,EAYvC,QAAW,OAAwB;AAClC,WAAO,KAAK;AAAA,MACX,KAAK,QAAQ,WAAW,KAAK;AAAA,MAC7B;AAAA,IACD,EAAE,QAAQ;AAAA,EACX;AAAA,EASU,qBAAqB,QAAiD;AAC/E,UAAM,QAAkB,CAAC;AAEzB,QAAI,OAAO,gBAAgB;AAC1B,YAAM,KAAK,mBAAmB,OAAO,cAAc,EAAE;AAAA,IACtD;AAEA,WAAO,MAAM,SAAS,iCAAsB,eAAI,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK;AAAA,EAC1E;AAAA,EAEU,uBAAuB,QAAiD;AACjF,UAAM,QAAkB,CAAC;AAEzB,QAAI,OAAO,wBAAwB;AAClC,YAAM,KAAK,0BAA0B;AAAA,IACtC;AAEA,QAAI,OAAO,YAAY;AACtB,YAAM,KAAK,OAAO,UAAU;AAAA,IAC7B;AAEA,WAAO,MAAM,SAAS,mCAAwB,eAAI,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK;AAAA,EAC5E;AACD;AAEO,MAAe,yBAKZ,wBAAqE;AAAA,EAG9E,YACC,SACA,SACU,QACS,aACnB,MACC;AACD,UAAM,SAAS,SAAS,QAAQ,IAAI;AAJ1B;AACS;AAAA,EAIpB;AAAA,EAVA,QAAiB,wBAAU,IAAY;AAAA,EAYvC,WAAkB;AACjB,UAAM,IAAI,uCAAyB;AAAA,EACpC;AAMD;", "names": []}