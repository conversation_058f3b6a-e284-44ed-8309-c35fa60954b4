import { Table } from "../table.cjs";
import type { Check } from "./checks.cjs";
import type { ForeignKey } from "./foreign-keys.cjs";
import type { Index } from "./indexes.cjs";
import type { PrimaryKey } from "./primary-keys.cjs";
import { MySqlTable } from "./table.cjs";
import { type UniqueConstraint } from "./unique-constraint.cjs";
import type { MySqlView } from "./view.cjs";
export declare function getTableConfig(table: MySqlTable): {
    columns: import("./index.ts").MySqlColumn<import("../column.ts").ColumnBaseConfig<import("../column-builder.ts").ColumnDataType, string>, object>[];
    indexes: Index[];
    foreignKeys: ForeignKey[];
    checks: Check[];
    primaryKeys: PrimaryKey[];
    uniqueConstraints: UniqueConstraint[];
    name: string;
    schema: string | undefined;
    baseName: string;
};
export declare function getViewConfig<TName extends string = string, TExisting extends boolean = boolean>(view: MySqlView<TName, TExisting>): {
    algorithm?: "undefined" | "merge" | "temptable" | undefined;
    definer?: string | undefined;
    sqlSecurity?: "definer" | "invoker" | undefined;
    withCheckOption?: "local" | "cascaded" | undefined;
    name: TName;
    originalName: TName;
    schema: string | undefined;
    selectedFields: import("../operations.ts").SelectedFields<import("../column.ts").AnyColumn, Table<import("../table.ts").TableConfig<import("../column.ts").Column<any, object, object>>>>;
    isExisting: TExisting;
    query: TExisting extends true ? undefined : import("../index.ts").SQL<unknown>;
    isAlias: boolean;
};
