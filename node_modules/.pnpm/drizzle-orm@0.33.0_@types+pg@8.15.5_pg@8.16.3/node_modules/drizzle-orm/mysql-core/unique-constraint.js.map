{"version": 3, "sources": ["../../src/mysql-core/unique-constraint.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { MySqlColumn } from './columns/index.ts';\nimport { MySqlTable } from './table.ts';\n\nexport function unique(name?: string): UniqueOnConstraintBuilder {\n\treturn new UniqueOnConstraintBuilder(name);\n}\n\nexport function uniqueKeyName(table: MySqlTable, columns: string[]) {\n\treturn `${table[MySqlTable.Symbol.Name]}_${columns.join('_')}_unique`;\n}\n\nexport class UniqueConstraintBuilder {\n\tstatic readonly [entityKind]: string = 'MySqlUniqueConstraintBuilder';\n\n\t/** @internal */\n\tcolumns: MySqlColumn[];\n\n\tconstructor(\n\t\tcolumns: MySqlColumn[],\n\t\tprivate name?: string,\n\t) {\n\t\tthis.columns = columns;\n\t}\n\n\t/** @internal */\n\tbuild(table: MySqlTable): UniqueConstraint {\n\t\treturn new UniqueConstraint(table, this.columns, this.name);\n\t}\n}\n\nexport class UniqueOnConstraintBuilder {\n\tstatic readonly [entityKind]: string = 'MySqlUniqueOnConstraintBuilder';\n\n\t/** @internal */\n\tname?: string;\n\n\tconstructor(\n\t\tname?: string,\n\t) {\n\t\tthis.name = name;\n\t}\n\n\ton(...columns: [MySqlColumn, ...MySqlColumn[]]) {\n\t\treturn new UniqueConstraintBuilder(columns, this.name);\n\t}\n}\n\nexport class UniqueConstraint {\n\tstatic readonly [entityKind]: string = 'MySqlUniqueConstraint';\n\n\treadonly columns: MySqlColumn[];\n\treadonly name?: string;\n\treadonly nullsNotDistinct: boolean = false;\n\n\tconstructor(readonly table: MySqlTable, columns: MySqlColumn[], name?: string) {\n\t\tthis.columns = columns;\n\t\tthis.name = name ?? uniqueKeyName(this.table, this.columns.map((column) => column.name));\n\t}\n\n\tgetName() {\n\t\treturn this.name;\n\t}\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAE3B,SAAS,kBAAkB;AAEpB,SAAS,OAAO,MAA0C;AAChE,SAAO,IAAI,0BAA0B,IAAI;AAC1C;AAEO,SAAS,cAAc,OAAmB,SAAmB;AACnE,SAAO,GAAG,MAAM,WAAW,OAAO,IAAI,CAAC,IAAI,QAAQ,KAAK,GAAG,CAAC;AAC7D;AAEO,MAAM,wBAAwB;AAAA,EAMpC,YACC,SACQ,MACP;AADO;AAER,SAAK,UAAU;AAAA,EAChB;AAAA,EAVA,QAAiB,UAAU,IAAY;AAAA;AAAA,EAGvC;AAAA;AAAA,EAUA,MAAM,OAAqC;AAC1C,WAAO,IAAI,iBAAiB,OAAO,KAAK,SAAS,KAAK,IAAI;AAAA,EAC3D;AACD;AAEO,MAAM,0BAA0B;AAAA,EACtC,QAAiB,UAAU,IAAY;AAAA;AAAA,EAGvC;AAAA,EAEA,YACC,MACC;AACD,SAAK,OAAO;AAAA,EACb;AAAA,EAEA,MAAM,SAA0C;AAC/C,WAAO,IAAI,wBAAwB,SAAS,KAAK,IAAI;AAAA,EACtD;AACD;AAEO,MAAM,iBAAiB;AAAA,EAO7B,YAAqB,OAAmB,SAAwB,MAAe;AAA1D;AACpB,SAAK,UAAU;AACf,SAAK,OAAO,QAAQ,cAAc,KAAK,OAAO,KAAK,QAAQ,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC;AAAA,EACxF;AAAA,EATA,QAAiB,UAAU,IAAY;AAAA,EAE9B;AAAA,EACA;AAAA,EACA,mBAA4B;AAAA,EAOrC,UAAU;AACT,WAAO,KAAK;AAAA,EACb;AACD;", "names": []}