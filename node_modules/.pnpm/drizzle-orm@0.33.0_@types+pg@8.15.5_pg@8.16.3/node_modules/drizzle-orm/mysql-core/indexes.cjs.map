{"version": 3, "sources": ["../../src/mysql-core/indexes.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { SQL } from '~/sql/sql.ts';\nimport type { AnyMySqlColumn, MySqlColumn } from './columns/index.ts';\nimport type { MySqlTable } from './table.ts';\n\ninterface IndexConfig {\n\tname: string;\n\n\tcolumns: IndexColumn[];\n\n\t/**\n\t * If true, the index will be created as `create unique index` instead of `create index`.\n\t */\n\tunique?: boolean;\n\n\t/**\n\t * If set, the index will be created as `create index ... using { 'btree' | 'hash' }`.\n\t */\n\tusing?: 'btree' | 'hash';\n\n\t/**\n\t * If set, the index will be created as `create index ... algorythm { 'default' | 'inplace' | 'copy' }`.\n\t */\n\talgorythm?: 'default' | 'inplace' | 'copy';\n\n\t/**\n\t * If set, adds locks to the index creation.\n\t */\n\tlock?: 'default' | 'none' | 'shared' | 'exclusive';\n}\n\nexport type IndexColumn = MySqlColumn | SQL;\n\nexport class IndexBuilderOn {\n\tstatic readonly [entityKind]: string = 'MySqlIndexBuilderOn';\n\n\tconstructor(private name: string, private unique: boolean) {}\n\n\ton(...columns: [IndexColumn, ...IndexColumn[]]): IndexBuilder {\n\t\treturn new IndexBuilder(this.name, columns, this.unique);\n\t}\n}\n\nexport interface AnyIndexBuilder {\n\tbuild(table: MySqlTable): Index;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface IndexBuilder extends AnyIndexBuilder {}\n\nexport class IndexBuilder implements AnyIndexBuilder {\n\tstatic readonly [entityKind]: string = 'MySqlIndexBuilder';\n\n\t/** @internal */\n\tconfig: IndexConfig;\n\n\tconstructor(name: string, columns: IndexColumn[], unique: boolean) {\n\t\tthis.config = {\n\t\t\tname,\n\t\t\tcolumns,\n\t\t\tunique,\n\t\t};\n\t}\n\n\tusing(using: IndexConfig['using']): this {\n\t\tthis.config.using = using;\n\t\treturn this;\n\t}\n\n\talgorythm(algorythm: IndexConfig['algorythm']): this {\n\t\tthis.config.algorythm = algorythm;\n\t\treturn this;\n\t}\n\n\tlock(lock: IndexConfig['lock']): this {\n\t\tthis.config.lock = lock;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tbuild(table: MySqlTable): Index {\n\t\treturn new Index(this.config, table);\n\t}\n}\n\nexport class Index {\n\tstatic readonly [entityKind]: string = 'MySqlIndex';\n\n\treadonly config: IndexConfig & { table: MySqlTable };\n\n\tconstructor(config: IndexConfig, table: MySqlTable) {\n\t\tthis.config = { ...config, table };\n\t}\n}\n\nexport type GetColumnsTableName<TColumns> = TColumns extends\n\tAnyMySqlColumn<{ tableName: infer TTableName extends string }> | AnyMySqlColumn<\n\t\t{ tableName: infer TTableName extends string }\n\t>[] ? TTableName\n\t: never;\n\nexport function index(name: string): IndexBuilderOn {\n\treturn new IndexBuilderOn(name, false);\n}\n\nexport function uniqueIndex(name: string): IndexBuilderOn {\n\treturn new IndexBuilderOn(name, true);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAiCpB,MAAM,eAAe;AAAA,EAG3B,YAAoB,MAAsB,QAAiB;AAAvC;AAAsB;AAAA,EAAkB;AAAA,EAF5D,QAAiB,wBAAU,IAAY;AAAA,EAIvC,MAAM,SAAwD;AAC7D,WAAO,IAAI,aAAa,KAAK,MAAM,SAAS,KAAK,MAAM;AAAA,EACxD;AACD;AASO,MAAM,aAAwC;AAAA,EACpD,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAGvC;AAAA,EAEA,YAAY,MAAc,SAAwB,QAAiB;AAClE,SAAK,SAAS;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,OAAmC;AACxC,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA,EAEA,UAAU,WAA2C;AACpD,SAAK,OAAO,YAAY;AACxB,WAAO;AAAA,EACR;AAAA,EAEA,KAAK,MAAiC;AACrC,SAAK,OAAO,OAAO;AACnB,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,MAAM,OAA0B;AAC/B,WAAO,IAAI,MAAM,KAAK,QAAQ,KAAK;AAAA,EACpC;AACD;AAEO,MAAM,MAAM;AAAA,EAClB,QAAiB,wBAAU,IAAY;AAAA,EAE9B;AAAA,EAET,YAAY,QAAqB,OAAmB;AACnD,SAAK,SAAS,EAAE,GAAG,QAAQ,MAAM;AAAA,EAClC;AACD;AAQO,SAAS,MAAM,MAA8B;AACnD,SAAO,IAAI,eAAe,MAAM,KAAK;AACtC;AAEO,SAAS,YAAY,MAA8B;AACzD,SAAO,IAAI,eAAe,MAAM,IAAI;AACrC;", "names": []}