{"version": 3, "sources": ["../../src/neon-http/session.ts"], "sourcesContent": ["import type { FullQueryResults, NeonQueryFunction, NeonQueryPromise } from '@neondatabase/serverless';\nimport type { BatchItem } from '~/batch.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { NoopLogger } from '~/logger.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport { PgTransaction } from '~/pg-core/index.ts';\nimport type { SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport type { PgQueryResultHKT, PgTransactionConfig, PreparedQueryConfig } from '~/pg-core/session.ts';\nimport { PgPreparedQuery as PgPreparedQuery, PgSession } from '~/pg-core/session.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport type { PreparedQuery } from '~/session.ts';\nimport { fillPlaceholders, type Query } from '~/sql/sql.ts';\nimport { mapResultRow } from '~/utils.ts';\n\nexport type NeonHttpClient = NeonQueryFunction<any, any>;\n\nconst rawQueryConfig = {\n\tarrayMode: false,\n\tfullResults: true,\n} as const;\nconst queryConfig = {\n\tarrayMode: true,\n\tfullResults: true,\n} as const;\n\nexport class NeonHttpPreparedQuery<T extends PreparedQueryConfig> extends PgPreparedQuery<T> {\n\tstatic readonly [entityKind]: string = 'NeonHttpPreparedQuery';\n\n\tconstructor(\n\t\tprivate client: NeonHttpClient,\n\t\tquery: Query,\n\t\tprivate logger: Logger,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t) {\n\t\tsuper(query);\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues);\n\n\t\tthis.logger.logQuery(this.query.sql, params);\n\n\t\tconst { fields, client, query, customResultMapper } = this;\n\n\t\tif (!fields && !customResultMapper) {\n\t\t\treturn client(query.sql, params, rawQueryConfig);\n\t\t}\n\n\t\tconst result = await client(query.sql, params, queryConfig);\n\n\t\treturn this.mapResult(result);\n\t}\n\n\toverride mapResult(result: unknown): unknown {\n\t\tif (!this.fields && !this.customResultMapper) {\n\t\t\treturn result;\n\t\t}\n\n\t\tconst rows = (result as FullQueryResults<true>).rows;\n\n\t\tif (this.customResultMapper) {\n\t\t\treturn this.customResultMapper(rows);\n\t\t}\n\n\t\treturn rows.map((row) => mapResultRow(this.fields!, row, this.joinsNotNullableMap));\n\t}\n\n\tall(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['all']> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues);\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn this.client(this.query.sql, params, rawQueryConfig).then((result) => result.rows);\n\t}\n\n\tvalues(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['values']> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues);\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn this.client(this.query.sql, params, { arrayMode: true, fullResults: true }).then((result) => result.rows);\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode() {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nexport interface NeonHttpSessionOptions {\n\tlogger?: Logger;\n}\n\nexport class NeonHttpSession<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgSession<NeonHttpQueryResultHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'NeonHttpSession';\n\n\tprivate logger: Logger;\n\n\tconstructor(\n\t\tprivate client: NeonHttpClient,\n\t\tdialect: PgDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: NeonHttpSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t): PgPreparedQuery<T> {\n\t\treturn new NeonHttpPreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery,\n\t\t\tthis.logger,\n\t\t\tfields,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\tasync batch<U extends BatchItem<'pg'>, T extends Readonly<[U, ...U[]]>>(queries: T) {\n\t\tconst preparedQueries: PreparedQuery[] = [];\n\t\tconst builtQueries: NeonQueryPromise<any, true>[] = [];\n\n\t\tfor (const query of queries) {\n\t\t\tconst preparedQuery = query._prepare();\n\t\t\tconst builtQuery = preparedQuery.getQuery();\n\t\t\tpreparedQueries.push(preparedQuery);\n\t\t\tbuiltQueries.push(\n\t\t\t\tthis.client(builtQuery.sql, builtQuery.params, {\n\t\t\t\t\tfullResults: true,\n\t\t\t\t\tarrayMode: preparedQuery.isResponseInArrayMode(),\n\t\t\t\t}),\n\t\t\t);\n\t\t}\n\n\t\tconst batchResults = await this.client.transaction(builtQueries, queryConfig);\n\n\t\treturn batchResults.map((result, i) => preparedQueries[i]!.mapResult(result, true));\n\t}\n\n\t// change return type to QueryRows<true>\n\tasync query(query: string, params: unknown[]): Promise<FullQueryResults<true>> {\n\t\tthis.logger.logQuery(query, params);\n\t\tconst result = await this.client(query, params, { arrayMode: true, fullResults: true });\n\t\treturn result;\n\t}\n\n\t// change return type to QueryRows<false>\n\tasync queryObjects(\n\t\tquery: string,\n\t\tparams: unknown[],\n\t): Promise<FullQueryResults<false>> {\n\t\treturn this.client(query, params, { arrayMode: false, fullResults: true });\n\t}\n\n\toverride async transaction<T>(\n\t\t_transaction: (tx: NeonTransaction<TFullSchema, TSchema>) => Promise<T>,\n\t\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\t\t_config: PgTransactionConfig = {},\n\t): Promise<T> {\n\t\tthrow new Error('No transactions support in neon-http driver');\n\t}\n}\n\nexport class NeonTransaction<\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n> extends PgTransaction<NeonHttpQueryResultHKT, TFullSchema, TSchema> {\n\tstatic readonly [entityKind]: string = 'NeonHttpTransaction';\n\n\toverride async transaction<T>(_transaction: (tx: NeonTransaction<TFullSchema, TSchema>) => Promise<T>): Promise<T> {\n\t\tthrow new Error('No transactions support in neon-http driver');\n\t\t// const savepointName = `sp${this.nestedIndex + 1}`;\n\t\t// const tx = new NeonTransaction(this.dialect, this.session, this.schema, this.nestedIndex + 1);\n\t\t// await tx.execute(sql.raw(`savepoint ${savepointName}`));\n\t\t// try {\n\t\t// \tconst result = await transaction(tx);\n\t\t// \tawait tx.execute(sql.raw(`release savepoint ${savepointName}`));\n\t\t// \treturn result;\n\t\t// } catch (e) {\n\t\t// \tawait tx.execute(sql.raw(`rollback to savepoint ${savepointName}`));\n\t\t// \tthrow e;\n\t\t// }\n\t}\n}\n\nexport type NeonHttpQueryResult<T> = Omit<FullQueryResults<false>, 'rows'> & { rows: T[] };\n\nexport interface NeonHttpQueryResultHKT extends PgQueryResultHKT {\n\ttype: NeonHttpQueryResult<this['row']>;\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,kBAAkB;AAE3B,SAAS,qBAAqB;AAG9B,SAAS,iBAAoC,iBAAiB;AAG9D,SAAS,wBAAoC;AAC7C,SAAS,oBAAoB;AAI7B,MAAM,iBAAiB;AAAA,EACtB,WAAW;AAAA,EACX,aAAa;AACd;AACA,MAAM,cAAc;AAAA,EACnB,WAAW;AAAA,EACX,aAAa;AACd;AAEO,MAAM,8BAA6D,gBAAmB;AAAA,EAG5F,YACS,QACR,OACQ,QACA,QACA,wBACA,oBACP;AACD,UAAM,KAAK;AAPH;AAEA;AACA;AACA;AACA;AAAA,EAGT;AAAA,EAXA,QAAiB,UAAU,IAAY;AAAA,EAavC,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,iBAAiB;AAEpE,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAE3C,UAAM,EAAE,QAAQ,QAAQ,OAAO,mBAAmB,IAAI;AAEtD,QAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,aAAO,OAAO,MAAM,KAAK,QAAQ,cAAc;AAAA,IAChD;AAEA,UAAM,SAAS,MAAM,OAAO,MAAM,KAAK,QAAQ,WAAW;AAE1D,WAAO,KAAK,UAAU,MAAM;AAAA,EAC7B;AAAA,EAES,UAAU,QAA0B;AAC5C,QAAI,CAAC,KAAK,UAAU,CAAC,KAAK,oBAAoB;AAC7C,aAAO;AAAA,IACR;AAEA,UAAM,OAAQ,OAAkC;AAEhD,QAAI,KAAK,oBAAoB;AAC5B,aAAO,KAAK,mBAAmB,IAAI;AAAA,IACpC;AAEA,WAAO,KAAK,IAAI,CAAC,QAAQ,aAAa,KAAK,QAAS,KAAK,KAAK,mBAAmB,CAAC;AAAA,EACnF;AAAA,EAEA,IAAI,oBAAyD,CAAC,GAAsB;AACnF,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,iBAAiB;AACpE,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,KAAK,OAAO,KAAK,MAAM,KAAK,QAAQ,cAAc,EAAE,KAAK,CAAC,WAAW,OAAO,IAAI;AAAA,EACxF;AAAA,EAEA,OAAO,oBAAyD,CAAC,GAAyB;AACzF,UAAM,SAAS,iBAAiB,KAAK,MAAM,QAAQ,iBAAiB;AACpE,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,KAAK,OAAO,KAAK,MAAM,KAAK,QAAQ,EAAE,WAAW,MAAM,aAAa,KAAK,CAAC,EAAE,KAAK,CAAC,WAAW,OAAO,IAAI;AAAA,EAChH;AAAA;AAAA,EAGA,wBAAwB;AACvB,WAAO,KAAK;AAAA,EACb;AACD;AAMO,MAAM,wBAGH,UAAwD;AAAA,EAKjE,YACS,QACR,SACQ,QACA,UAAkC,CAAC,GAC1C;AACD,UAAM,OAAO;AALL;AAEA;AACA;AAGR,SAAK,SAAS,QAAQ,UAAU,IAAI,WAAW;AAAA,EAChD;AAAA,EAZA,QAAiB,UAAU,IAAY;AAAA,EAE/B;AAAA,EAYR,aACC,OACA,QACA,MACA,uBACA,oBACqB;AACrB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL;AAAA,MACA,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,MAAkE,SAAY;AACnF,UAAM,kBAAmC,CAAC;AAC1C,UAAM,eAA8C,CAAC;AAErD,eAAW,SAAS,SAAS;AAC5B,YAAM,gBAAgB,MAAM,SAAS;AACrC,YAAM,aAAa,cAAc,SAAS;AAC1C,sBAAgB,KAAK,aAAa;AAClC,mBAAa;AAAA,QACZ,KAAK,OAAO,WAAW,KAAK,WAAW,QAAQ;AAAA,UAC9C,aAAa;AAAA,UACb,WAAW,cAAc,sBAAsB;AAAA,QAChD,CAAC;AAAA,MACF;AAAA,IACD;AAEA,UAAM,eAAe,MAAM,KAAK,OAAO,YAAY,cAAc,WAAW;AAE5E,WAAO,aAAa,IAAI,CAAC,QAAQ,MAAM,gBAAgB,CAAC,EAAG,UAAU,QAAQ,IAAI,CAAC;AAAA,EACnF;AAAA;AAAA,EAGA,MAAM,MAAM,OAAe,QAAoD;AAC9E,SAAK,OAAO,SAAS,OAAO,MAAM;AAClC,UAAM,SAAS,MAAM,KAAK,OAAO,OAAO,QAAQ,EAAE,WAAW,MAAM,aAAa,KAAK,CAAC;AACtF,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,MAAM,aACL,OACA,QACmC;AACnC,WAAO,KAAK,OAAO,OAAO,QAAQ,EAAE,WAAW,OAAO,aAAa,KAAK,CAAC;AAAA,EAC1E;AAAA,EAEA,MAAe,YACd,cAEA,UAA+B,CAAC,GACnB;AACb,UAAM,IAAI,MAAM,6CAA6C;AAAA,EAC9D;AACD;AAEO,MAAM,wBAGH,cAA4D;AAAA,EACrE,QAAiB,UAAU,IAAY;AAAA,EAEvC,MAAe,YAAe,cAAqF;AAClH,UAAM,IAAI,MAAM,6CAA6C;AAAA,EAY9D;AACD;", "names": []}