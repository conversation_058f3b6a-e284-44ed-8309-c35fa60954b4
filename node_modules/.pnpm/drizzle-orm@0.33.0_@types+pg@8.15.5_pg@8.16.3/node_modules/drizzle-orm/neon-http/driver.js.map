{"version": 3, "sources": ["../../src/neon-http/driver.ts"], "sourcesContent": ["import type { NeonQueryFunction } from '@neondatabase/serverless';\nimport { types } from '@neondatabase/serverless';\nimport type { BatchItem, BatchResponse } from '~/batch.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { PgDatabase } from '~/pg-core/db.ts';\nimport { PgDialect } from '~/pg-core/dialect.ts';\nimport { createTableRelationsHelpers, extractTablesRelationalConfig } from '~/relations.ts';\nimport type { ExtractTablesWithRelations, RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport { type NeonHttpClient, type NeonHttpQueryResultHKT, NeonHttpSession } from './session.ts';\n\nexport interface NeonDriverOptions {\n\tlogger?: Logger;\n}\n\nexport class NeonHttpDriver {\n\tstatic readonly [entityKind]: string = 'NeonDriver';\n\n\tconstructor(\n\t\tprivate client: NeonHttpClient,\n\t\tprivate dialect: PgDialect,\n\t\tprivate options: NeonDriverOptions = {},\n\t) {\n\t\tthis.initMappers();\n\t}\n\n\tcreateSession(\n\t\tschema: RelationalSchemaConfig<TablesRelationalConfig> | undefined,\n\t): NeonHttpSession<Record<string, unknown>, TablesRelationalConfig> {\n\t\treturn new NeonHttpSession(this.client, this.dialect, schema, { logger: this.options.logger });\n\t}\n\n\tinitMappers() {\n\t\ttypes.setTypeParser(types.builtins.TIMESTAMPTZ, (val) => val);\n\t\ttypes.setTypeParser(types.builtins.TIMESTAMP, (val) => val);\n\t\ttypes.setTypeParser(types.builtins.DATE, (val) => val);\n\t\ttypes.setTypeParser(types.builtins.INTERVAL, (val) => val);\n\t}\n}\n\nexport class NeonHttpDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends PgDatabase<NeonHttpQueryResultHKT, TSchema> {\n\tstatic readonly [entityKind]: string = 'NeonHttpDatabase';\n\n\t/** @internal */\n\tdeclare readonly session: NeonHttpSession<TSchema, ExtractTablesWithRelations<TSchema>>;\n\n\tasync batch<U extends BatchItem<'pg'>, T extends Readonly<[U, ...U[]]>>(\n\t\tbatch: T,\n\t): Promise<BatchResponse<T>> {\n\t\treturn this.session.batch(batch) as Promise<BatchResponse<T>>;\n\t}\n}\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: NeonQueryFunction<any, any>,\n\tconfig: DrizzleConfig<TSchema> = {},\n): NeonHttpDatabase<TSchema> {\n\tconst dialect = new PgDialect();\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst driver = new NeonHttpDriver(client, dialect, { logger });\n\tconst session = driver.createSession(schema);\n\n\treturn new NeonHttpDatabase(\n\t\tdialect,\n\t\tsession,\n\t\tschema as RelationalSchemaConfig<ExtractTablesWithRelations<TSchema>> | undefined,\n\t);\n}\n"], "mappings": "AACA,SAAS,aAAa;AAEtB,SAAS,kBAAkB;AAE3B,SAAS,qBAAqB;AAC9B,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;AAC1B,SAAS,6BAA6B,qCAAqC;AAG3E,SAA2D,uBAAuB;AAM3E,MAAM,eAAe;AAAA,EAG3B,YACS,QACA,SACA,UAA6B,CAAC,GACrC;AAHO;AACA;AACA;AAER,SAAK,YAAY;AAAA,EAClB;AAAA,EARA,QAAiB,UAAU,IAAY;AAAA,EAUvC,cACC,QACmE;AACnE,WAAO,IAAI,gBAAgB,KAAK,QAAQ,KAAK,SAAS,QAAQ,EAAE,QAAQ,KAAK,QAAQ,OAAO,CAAC;AAAA,EAC9F;AAAA,EAEA,cAAc;AACb,UAAM,cAAc,MAAM,SAAS,aAAa,CAAC,QAAQ,GAAG;AAC5D,UAAM,cAAc,MAAM,SAAS,WAAW,CAAC,QAAQ,GAAG;AAC1D,UAAM,cAAc,MAAM,SAAS,MAAM,CAAC,QAAQ,GAAG;AACrD,UAAM,cAAc,MAAM,SAAS,UAAU,CAAC,QAAQ,GAAG;AAAA,EAC1D;AACD;AAEO,MAAM,yBAEH,WAA4C;AAAA,EACrD,QAAiB,UAAU,IAAY;AAAA,EAKvC,MAAM,MACL,OAC4B;AAC5B,WAAO,KAAK,QAAQ,MAAM,KAAK;AAAA,EAChC;AACD;AAEO,SAAS,QACf,QACA,SAAiC,CAAC,GACN;AAC5B,QAAM,UAAU,IAAI,UAAU;AAC9B,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,eAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,SAAS,IAAI,eAAe,QAAQ,SAAS,EAAE,OAAO,CAAC;AAC7D,QAAM,UAAU,OAAO,cAAc,MAAM;AAE3C,SAAO,IAAI;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;", "names": []}