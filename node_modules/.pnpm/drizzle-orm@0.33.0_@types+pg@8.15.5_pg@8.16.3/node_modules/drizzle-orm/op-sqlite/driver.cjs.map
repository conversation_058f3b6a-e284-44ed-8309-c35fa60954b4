{"version": 3, "sources": ["../../src/op-sqlite/driver.ts"], "sourcesContent": ["import type { OPSQLiteConnection, QueryResult } from '@op-engineering/op-sqlite';\nimport { DefaultLogger } from '~/logger.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { BaseSQLiteDatabase } from '~/sqlite-core/db.ts';\nimport { SQLiteAsyncDialect } from '~/sqlite-core/dialect.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport { OPSQLiteSession } from './session.ts';\n\nexport type OPSQLiteDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> = BaseSQLiteDatabase<'async', QueryResult, TSchema>;\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: OPSQLiteConnection,\n\tconfig: DrizzleConfig<TSchema> = {},\n): OPSQLiteDatabase<TSchema> {\n\tconst dialect = new SQLiteAsyncDialect();\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new OPSQLiteSession(client, dialect, schema, { logger });\n\treturn new BaseSQLiteDatabase('async', dialect, session, schema) as OPSQLiteDatabase<TSchema>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA8B;AAC9B,uBAKO;AACP,gBAAmC;AACnC,qBAAmC;AAEnC,qBAAgC;AAMzB,SAAS,QACf,QACA,SAAiC,CAAC,GACN;AAC5B,QAAM,UAAU,IAAI,kCAAmB;AACvC,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,4BAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,mBAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,+BAAgB,QAAQ,SAAS,QAAQ,EAAE,OAAO,CAAC;AACvE,SAAO,IAAI,6BAAmB,SAAS,SAAS,SAAS,MAAM;AAChE;", "names": []}