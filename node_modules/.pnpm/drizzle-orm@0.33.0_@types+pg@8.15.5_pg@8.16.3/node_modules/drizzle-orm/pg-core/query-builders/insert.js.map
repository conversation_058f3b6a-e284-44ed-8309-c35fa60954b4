{"version": 3, "sources": ["../../../src/pg-core/query-builders/insert.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type { IndexColumn } from '~/pg-core/indexes.ts';\nimport type {\n\tPgPreparedQuery,\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport type { PgTable } from '~/pg-core/table.ts';\nimport type { SelectResultFields } from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Placeholder, Query, SQLWrapper } from '~/sql/sql.ts';\nimport { Param, SQL, sql } from '~/sql/sql.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport { tracer } from '~/tracing.ts';\nimport { mapUpdateSet, orderSelectedFields } from '~/utils.ts';\nimport type { PgColumn } from '../columns/common.ts';\nimport type { SelectedFieldsFlat, SelectedFieldsOrdered } from './select.types.ts';\nimport type { PgUpdateSetSource } from './update.ts';\n\nexport interface PgInsertConfig<TTable extends PgTable = PgTable> {\n\ttable: TTable;\n\tvalues: Record<string, Param | SQL>[];\n\twithList?: Subquery[];\n\tonConflict?: SQL;\n\treturning?: SelectedFieldsOrdered;\n}\n\nexport type PgInsertValue<TTable extends PgTable> =\n\t& {\n\t\t[Key in keyof TTable['$inferInsert']]: TTable['$inferInsert'][Key] | SQL | Placeholder;\n\t}\n\t& {};\n\nexport class PgInsertBuilder<TTable extends PgTable, TQueryResult extends PgQueryResultHKT> {\n\tstatic readonly [entityKind]: string = 'PgInsertBuilder';\n\n\tconstructor(\n\t\tprivate table: TTable,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\tprivate withList?: Subquery[],\n\t) {}\n\n\tvalues(value: PgInsertValue<TTable>): PgInsertBase<TTable, TQueryResult>;\n\tvalues(values: PgInsertValue<TTable>[]): PgInsertBase<TTable, TQueryResult>;\n\tvalues(values: PgInsertValue<TTable> | PgInsertValue<TTable>[]): PgInsertBase<TTable, TQueryResult> {\n\t\tvalues = Array.isArray(values) ? values : [values];\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('values() must be called with at least one value');\n\t\t}\n\t\tconst mappedValues = values.map((entry) => {\n\t\t\tconst result: Record<string, Param | SQL> = {};\n\t\t\tconst cols = this.table[Table.Symbol.Columns];\n\t\t\tfor (const colKey of Object.keys(entry)) {\n\t\t\t\tconst colValue = entry[colKey as keyof typeof entry];\n\t\t\t\tresult[colKey] = is(colValue, SQL) ? colValue : new Param(colValue, cols[colKey]);\n\t\t\t}\n\t\t\treturn result;\n\t\t});\n\n\t\treturn new PgInsertBase(this.table, mappedValues, this.session, this.dialect, this.withList);\n\t}\n}\n\nexport type PgInsertWithout<T extends AnyPgInsert, TDynamic extends boolean, K extends keyof T & string> =\n\tTDynamic extends true ? T\n\t\t: Omit<\n\t\t\tPgInsertBase<\n\t\t\t\tT['_']['table'],\n\t\t\t\tT['_']['queryResult'],\n\t\t\t\tT['_']['returning'],\n\t\t\t\tTDynamic,\n\t\t\t\tT['_']['excludedMethods'] | K\n\t\t\t>,\n\t\t\tT['_']['excludedMethods'] | K\n\t\t>;\n\nexport type PgInsertReturning<\n\tT extends AnyPgInsert,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFieldsFlat,\n> = PgInsertBase<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tSelectResultFields<TSelectedFields>,\n\tTDynamic,\n\tT['_']['excludedMethods']\n>;\n\nexport type PgInsertReturningAll<T extends AnyPgInsert, TDynamic extends boolean> = PgInsertBase<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['table']['$inferSelect'],\n\tTDynamic,\n\tT['_']['excludedMethods']\n>;\n\nexport interface PgInsertOnConflictDoUpdateConfig<T extends AnyPgInsert> {\n\ttarget: IndexColumn | IndexColumn[];\n\t/** @deprecated use either `targetWhere` or `setWhere` */\n\twhere?: SQL;\n\t// TODO: add tests for targetWhere and setWhere\n\ttargetWhere?: SQL;\n\tsetWhere?: SQL;\n\tset: PgUpdateSetSource<T['_']['table']>;\n}\n\nexport type PgInsertPrepare<T extends AnyPgInsert> = PgPreparedQuery<\n\tPreparedQueryConfig & {\n\t\texecute: T['_']['returning'] extends undefined ? PgQueryResultKind<T['_']['queryResult'], never>\n\t\t\t: T['_']['returning'][];\n\t}\n>;\n\nexport type PgInsertDynamic<T extends AnyPgInsert> = PgInsert<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['returning']\n>;\n\nexport type AnyPgInsert = PgInsertBase<any, any, any, any, any>;\n\nexport type PgInsert<\n\tTTable extends PgTable = PgTable,\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = Record<string, unknown> | undefined,\n> = PgInsertBase<TTable, TQueryResult, TReturning, true, never>;\n\nexport interface PgInsertBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tQueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>,\n\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly table: TTable;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t};\n}\n\nexport class PgInsertBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>\n\timplements\n\t\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\t\tSQLWrapper\n{\n\tstatic readonly [entityKind]: string = 'PgInsert';\n\n\tprivate config: PgInsertConfig<TTable>;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tvalues: PgInsertConfig['values'],\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\twithList?: Subquery[],\n\t) {\n\t\tsuper();\n\t\tthis.config = { table, values, withList };\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the inserted rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#insert-returning}\n\t *\n\t * @example\n\t * ```ts\n\t * // Insert one row and return all fields\n\t * const insertedCar: Car[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning();\n\t *\n\t * // Insert one row and return only the id\n\t * const insertedCarId: { id: number }[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning({ id: cars.id });\n\t * ```\n\t */\n\treturning(): PgInsertWithout<PgInsertReturningAll<this, TDynamic>, TDynamic, 'returning'>;\n\treturning<TSelectedFields extends SelectedFieldsFlat>(\n\t\tfields: TSelectedFields,\n\t): PgInsertWithout<PgInsertReturning<this, TDynamic, TSelectedFields>, TDynamic, 'returning'>;\n\treturning(\n\t\tfields: SelectedFieldsFlat = this.config.table[Table.Symbol.Columns],\n\t): PgInsertWithout<AnyPgInsert, TDynamic, 'returning'> {\n\t\tthis.config.returning = orderSelectedFields<PgColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `on conflict do nothing` clause to the query.\n\t *\n\t * Calling this method simply avoids inserting a row as its alternative action.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#on-conflict-do-nothing}\n\t *\n\t * @param config The `target` and `where` clauses.\n\t *\n\t * @example\n\t * ```ts\n\t * // Insert one row and cancel the insert if there's a conflict\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoNothing();\n\t *\n\t * // Explicitly specify conflict target\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoNothing({ target: cars.id });\n\t * ```\n\t */\n\tonConflictDoNothing(\n\t\tconfig: { target?: IndexColumn | IndexColumn[]; where?: SQL } = {},\n\t): PgInsertWithout<this, TDynamic, 'onConflictDoNothing' | 'onConflictDoUpdate'> {\n\t\tif (config.target === undefined) {\n\t\t\tthis.config.onConflict = sql`do nothing`;\n\t\t} else {\n\t\t\tlet targetColumn = '';\n\t\t\ttargetColumn = Array.isArray(config.target)\n\t\t\t\t? config.target.map((it) => this.dialect.escapeName(it.name)).join(',')\n\t\t\t\t: this.dialect.escapeName(config.target.name);\n\n\t\t\tconst whereSql = config.where ? sql` where ${config.where}` : undefined;\n\t\t\tthis.config.onConflict = sql`(${sql.raw(targetColumn)})${whereSql} do nothing`;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `on conflict do update` clause to the query.\n\t *\n\t * Calling this method will update the existing row that conflicts with the row proposed for insertion as its alternative action.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#upserts-and-conflicts}\n\t *\n\t * @param config The `target`, `set` and `where` clauses.\n\t *\n\t * @example\n\t * ```ts\n\t * // Update the row if there's a conflict\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoUpdate({\n\t *     target: cars.id,\n\t *     set: { brand: 'Porsche' }\n\t *   });\n\t *\n\t * // Upsert with 'where' clause\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoUpdate({\n\t *     target: cars.id,\n\t *     set: { brand: 'newBMW' },\n\t *     targetWhere: sql`${cars.createdAt} > '2023-01-01'::date`,\n\t *   });\n\t * ```\n\t */\n\tonConflictDoUpdate(\n\t\tconfig: PgInsertOnConflictDoUpdateConfig<this>,\n\t): PgInsertWithout<this, TDynamic, 'onConflictDoNothing' | 'onConflictDoUpdate'> {\n\t\tif (config.where && (config.targetWhere || config.setWhere)) {\n\t\t\tthrow new Error(\n\t\t\t\t'You cannot use both \"where\" and \"targetWhere\"/\"setWhere\" at the same time - \"where\" is deprecated, use \"targetWhere\" or \"setWhere\" instead.',\n\t\t\t);\n\t\t}\n\t\tconst whereSql = config.where ? sql` where ${config.where}` : undefined;\n\t\tconst targetWhereSql = config.targetWhere ? sql` where ${config.targetWhere}` : undefined;\n\t\tconst setWhereSql = config.setWhere ? sql` where ${config.setWhere}` : undefined;\n\t\tconst setSql = this.dialect.buildUpdateSet(this.config.table, mapUpdateSet(this.config.table, config.set));\n\t\tlet targetColumn = '';\n\t\ttargetColumn = Array.isArray(config.target)\n\t\t\t? config.target.map((it) => this.dialect.escapeName(it.name)).join(',')\n\t\t\t: this.dialect.escapeName(config.target.name);\n\t\tthis.config.onConflict = sql`(${\n\t\t\tsql.raw(targetColumn)\n\t\t})${targetWhereSql} do update set ${setSql}${whereSql}${setWhereSql}`;\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildInsertQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgInsertPrepare<this> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\treturn this.session.prepareQuery<\n\t\t\t\tPreparedQueryConfig & {\n\t\t\t\t\texecute: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t\t\t\t}\n\t\t\t>(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true);\n\t\t});\n\t}\n\n\tprepare(name: string): PgInsertPrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues);\n\t\t});\n\t};\n\n\t$dynamic(): PgInsertDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n"], "mappings": "AAAA,SAAS,YAAY,UAAU;AAY/B,SAAS,oBAAoB;AAG7B,SAAS,OAAO,KAAK,WAAW;AAEhC,SAAS,aAAa;AACtB,SAAS,cAAc;AACvB,SAAS,cAAc,2BAA2B;AAmB3C,MAAM,gBAA+E;AAAA,EAG3F,YACS,OACA,SACA,SACA,UACP;AAJO;AACA;AACA;AACA;AAAA,EACN;AAAA,EAPH,QAAiB,UAAU,IAAY;AAAA,EAWvC,OAAO,QAA6F;AACnG,aAAS,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AACjD,QAAI,OAAO,WAAW,GAAG;AACxB,YAAM,IAAI,MAAM,iDAAiD;AAAA,IAClE;AACA,UAAM,eAAe,OAAO,IAAI,CAAC,UAAU;AAC1C,YAAM,SAAsC,CAAC;AAC7C,YAAM,OAAO,KAAK,MAAM,MAAM,OAAO,OAAO;AAC5C,iBAAW,UAAU,OAAO,KAAK,KAAK,GAAG;AACxC,cAAM,WAAW,MAAM,MAA4B;AACnD,eAAO,MAAM,IAAI,GAAG,UAAU,GAAG,IAAI,WAAW,IAAI,MAAM,UAAU,KAAK,MAAM,CAAC;AAAA,MACjF;AACA,aAAO;AAAA,IACR,CAAC;AAED,WAAO,IAAI,aAAa,KAAK,OAAO,cAAc,KAAK,SAAS,KAAK,SAAS,KAAK,QAAQ;AAAA,EAC5F;AACD;AAwFO,MAAM,qBAQH,aAIV;AAAA,EAKC,YACC,OACA,QACQ,SACA,SACR,UACC;AACD,UAAM;AAJE;AACA;AAIR,SAAK,SAAS,EAAE,OAAO,QAAQ,SAAS;AAAA,EACzC;AAAA,EAbA,QAAiB,UAAU,IAAY;AAAA,EAE/B;AAAA,EAqCR,UACC,SAA6B,KAAK,OAAO,MAAM,MAAM,OAAO,OAAO,GACb;AACtD,SAAK,OAAO,YAAY,oBAA8B,MAAM;AAC5D,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwBA,oBACC,SAAgE,CAAC,GACe;AAChF,QAAI,OAAO,WAAW,QAAW;AAChC,WAAK,OAAO,aAAa;AAAA,IAC1B,OAAO;AACN,UAAI,eAAe;AACnB,qBAAe,MAAM,QAAQ,OAAO,MAAM,IACvC,OAAO,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,WAAW,GAAG,IAAI,CAAC,EAAE,KAAK,GAAG,IACpE,KAAK,QAAQ,WAAW,OAAO,OAAO,IAAI;AAE7C,YAAM,WAAW,OAAO,QAAQ,aAAa,OAAO,KAAK,KAAK;AAC9D,WAAK,OAAO,aAAa,OAAO,IAAI,IAAI,YAAY,CAAC,IAAI,QAAQ;AAAA,IAClE;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+BA,mBACC,QACgF;AAChF,QAAI,OAAO,UAAU,OAAO,eAAe,OAAO,WAAW;AAC5D,YAAM,IAAI;AAAA,QACT;AAAA,MACD;AAAA,IACD;AACA,UAAM,WAAW,OAAO,QAAQ,aAAa,OAAO,KAAK,KAAK;AAC9D,UAAM,iBAAiB,OAAO,cAAc,aAAa,OAAO,WAAW,KAAK;AAChF,UAAM,cAAc,OAAO,WAAW,aAAa,OAAO,QAAQ,KAAK;AACvE,UAAM,SAAS,KAAK,QAAQ,eAAe,KAAK,OAAO,OAAO,aAAa,KAAK,OAAO,OAAO,OAAO,GAAG,CAAC;AACzG,QAAI,eAAe;AACnB,mBAAe,MAAM,QAAQ,OAAO,MAAM,IACvC,OAAO,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,WAAW,GAAG,IAAI,CAAC,EAAE,KAAK,GAAG,IACpE,KAAK,QAAQ,WAAW,OAAO,OAAO,IAAI;AAC7C,SAAK,OAAO,aAAa,OACxB,IAAI,IAAI,YAAY,CACrB,IAAI,cAAc,kBAAkB,MAAM,GAAG,QAAQ,GAAG,WAAW;AACnE,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;AAAA,EACjD;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAS,MAAsC;AAC9C,WAAO,OAAO,gBAAgB,wBAAwB,MAAM;AAC3D,aAAO,KAAK,QAAQ,aAIlB,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,WAAW,MAAM,IAAI;AAAA,IAC5E,CAAC;AAAA,EACF;AAAA,EAEA,QAAQ,MAAqC;AAC5C,WAAO,KAAK,SAAS,IAAI;AAAA,EAC1B;AAAA,EAES,UAAkD,CAAC,sBAAsB;AACjF,WAAO,OAAO,gBAAgB,qBAAqB,MAAM;AACxD,aAAO,KAAK,SAAS,EAAE,QAAQ,iBAAiB;AAAA,IACjD,CAAC;AAAA,EACF;AAAA,EAEA,WAAkC;AACjC,WAAO;AAAA,EACR;AACD;", "names": []}