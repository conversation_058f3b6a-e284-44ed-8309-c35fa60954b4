{"version": 3, "sources": ["../../../src/pg-core/query-builders/delete.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type {\n\tPgPreparedQuery,\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport type { PgTable } from '~/pg-core/table.ts';\nimport type { SelectResultFields } from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Query, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport { tracer } from '~/tracing.ts';\nimport { orderSelectedFields } from '~/utils.ts';\nimport type { PgColumn } from '../columns/common.ts';\nimport type { SelectedFieldsFlat, SelectedFieldsOrdered } from './select.types.ts';\n\nexport type PgDeleteWithout<\n\tT extends AnyPgDeleteBase,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n> = TDynamic extends true ? T\n\t: Omit<\n\t\tPgDeleteBase<\n\t\t\tT['_']['table'],\n\t\t\tT['_']['queryResult'],\n\t\t\tT['_']['returning'],\n\t\t\tTDynamic,\n\t\t\tT['_']['excludedMethods'] | K\n\t\t>,\n\t\tT['_']['excludedMethods'] | K\n\t>;\n\nexport type PgDelete<\n\tTTable extends PgTable = PgTable,\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = Record<string, unknown> | undefined,\n> = PgDeleteBase<TTable, TQueryResult, TReturning, true, never>;\n\nexport interface PgDeleteConfig {\n\twhere?: SQL | undefined;\n\ttable: PgTable;\n\treturning?: SelectedFieldsOrdered;\n\twithList?: Subquery[];\n}\n\nexport type PgDeleteReturningAll<\n\tT extends AnyPgDeleteBase,\n\tTDynamic extends boolean,\n> = PgDeleteWithout<\n\tPgDeleteBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['table']['$inferSelect'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type PgDeleteReturning<\n\tT extends AnyPgDeleteBase,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFieldsFlat,\n> = PgDeleteWithout<\n\tPgDeleteBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tSelectResultFields<TSelectedFields>,\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type PgDeletePrepare<T extends AnyPgDeleteBase> = PgPreparedQuery<\n\tPreparedQueryConfig & {\n\t\texecute: T['_']['returning'] extends undefined ? PgQueryResultKind<T['_']['queryResult'], never>\n\t\t\t: T['_']['returning'][];\n\t}\n>;\n\nexport type PgDeleteDynamic<T extends AnyPgDeleteBase> = PgDelete<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['returning']\n>;\n\nexport type AnyPgDeleteBase = PgDeleteBase<any, any, any, any, any>;\n\nexport interface PgDeleteBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tQueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>,\n\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\tSQLWrapper\n{\n\treadonly _: {\n\t\tdialect: 'pg';\n\t\treadonly table: TTable;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t};\n}\n\nexport class PgDeleteBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>\n\timplements\n\t\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\t\tSQLWrapper\n{\n\tstatic readonly [entityKind]: string = 'PgDelete';\n\n\tprivate config: PgDeleteConfig;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\twithList?: Subquery[],\n\t) {\n\t\tsuper();\n\t\tthis.config = { table, withList };\n\t}\n\n\t/**\n\t * Adds a `where` clause to the query.\n\t *\n\t * Calling this method will delete only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t *\n\t * @param where the `where` clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be deleted.\n\t *\n\t * ```ts\n\t * // Delete all cars with green color\n\t * await db.delete(cars).where(eq(cars.color, 'green'));\n\t * // or\n\t * await db.delete(cars).where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Delete all BMW cars with a green color\n\t * await db.delete(cars).where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Delete all cars with the green or blue color\n\t * await db.delete(cars).where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(where: SQL | undefined): PgDeleteWithout<this, TDynamic, 'where'> {\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the deleted rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/delete#delete-with-return}\n\t *\n\t * @example\n\t * ```ts\n\t * // Delete all cars with the green color and return all fields\n\t * const deletedCars: Car[] = await db.delete(cars)\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning();\n\t *\n\t * // Delete all cars with the green color and return only their id and brand fields\n\t * const deletedCarsIdsAndBrands: { id: number, brand: string }[] = await db.delete(cars)\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning({ id: cars.id, brand: cars.brand });\n\t * ```\n\t */\n\treturning(): PgDeleteReturningAll<this, TDynamic>;\n\treturning<TSelectedFields extends SelectedFieldsFlat>(\n\t\tfields: TSelectedFields,\n\t): PgDeleteReturning<this, TDynamic, TSelectedFields>;\n\treturning(\n\t\tfields: SelectedFieldsFlat = this.config.table[Table.Symbol.Columns],\n\t): PgDeleteReturning<this, TDynamic, any> {\n\t\tthis.config.returning = orderSelectedFields<PgColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildDeleteQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgDeletePrepare<this> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\treturn this.session.prepareQuery<\n\t\t\t\tPreparedQueryConfig & {\n\t\t\t\t\texecute: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t\t\t\t}\n\t\t\t>(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true);\n\t\t});\n\t}\n\n\tprepare(name: string): PgDeletePrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues);\n\t\t});\n\t};\n\n\t$dynamic(): PgDeleteDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAW3B,2BAA6B;AAI7B,mBAAsB;AACtB,qBAAuB;AACvB,mBAAoC;AAqG7B,MAAM,qBAOH,kCAIV;AAAA,EAKC,YACC,OACQ,SACA,SACR,UACC;AACD,UAAM;AAJE;AACA;AAIR,SAAK,SAAS,EAAE,OAAO,SAAS;AAAA,EACjC;AAAA,EAZA,QAAiB,wBAAU,IAAY;AAAA,EAE/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAyCR,MAAM,OAAkE;AACvE,SAAK,OAAO,QAAQ;AACpB,WAAO;AAAA,EACR;AAAA,EA0BA,UACC,SAA6B,KAAK,OAAO,MAAM,mBAAM,OAAO,OAAO,GAC1B;AACzC,SAAK,OAAO,gBAAY,kCAA8B,MAAM;AAC5D,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;AAAA,EACjD;AAAA,EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,SAAS,MAAsC;AAC9C,WAAO,sBAAO,gBAAgB,wBAAwB,MAAM;AAC3D,aAAO,KAAK,QAAQ,aAIlB,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,WAAW,MAAM,IAAI;AAAA,IAC5E,CAAC;AAAA,EACF;AAAA,EAEA,QAAQ,MAAqC;AAC5C,WAAO,KAAK,SAAS,IAAI;AAAA,EAC1B;AAAA,EAES,UAAkD,CAAC,sBAAsB;AACjF,WAAO,sBAAO,gBAAgB,qBAAqB,MAAM;AACxD,aAAO,KAAK,SAAS,EAAE,QAAQ,iBAAiB;AAAA,IACjD,CAAC;AAAA,EACF;AAAA,EAEA,WAAkC;AACjC,WAAO;AAAA,EACR;AACD;", "names": []}