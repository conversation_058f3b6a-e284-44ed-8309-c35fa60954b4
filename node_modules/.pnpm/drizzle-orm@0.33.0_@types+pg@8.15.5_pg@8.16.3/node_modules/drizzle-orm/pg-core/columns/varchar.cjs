"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var varchar_exports = {};
__export(varchar_exports, {
  PgVarchar: () => PgVarchar,
  PgVarcharBuilder: () => PgVarcharBuilder,
  varchar: () => varchar
});
module.exports = __toCommonJS(varchar_exports);
var import_entity = require("../../entity.cjs");
var import_common = require("./common.cjs");
class PgVarcharBuilder extends import_common.PgColumnBuilder {
  static [import_entity.entityKind] = "PgVarcharBuilder";
  constructor(name, config) {
    super(name, "string", "PgVarchar");
    this.config.length = config.length;
    this.config.enumValues = config.enum;
  }
  /** @internal */
  build(table) {
    return new PgVarchar(table, this.config);
  }
}
class PgVarchar extends import_common.PgColumn {
  static [import_entity.entityKind] = "PgVarchar";
  length = this.config.length;
  enumValues = this.config.enumValues;
  getSQLType() {
    return this.length === void 0 ? `varchar` : `varchar(${this.length})`;
  }
}
function varchar(name, config = {}) {
  return new PgVarcharBuilder(name, config);
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  PgVarchar,
  PgVarcharBuilder,
  varchar
});
//# sourceMappingURL=varchar.cjs.map