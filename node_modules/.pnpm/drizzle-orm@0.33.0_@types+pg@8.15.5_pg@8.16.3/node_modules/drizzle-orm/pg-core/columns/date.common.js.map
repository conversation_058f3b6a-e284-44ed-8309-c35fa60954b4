{"version": 3, "sources": ["../../../src/pg-core/columns/date.common.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnDataType } from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport { sql } from '~/sql/sql.ts';\nimport { PgColumnBuilder } from './common.ts';\n\nexport abstract class PgDateColumnBaseBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n> extends PgColumnBuilder<T, TRuntimeConfig> {\n\tstatic readonly [entityKind]: string = 'PgDateColumnBaseBuilder';\n\n\tdefaultNow() {\n\t\treturn this.default(sql`now()`);\n\t}\n}\n"], "mappings": "AACA,SAAS,kBAAkB;AAC3B,SAAS,WAAW;AACpB,SAAS,uBAAuB;AAEzB,MAAe,gCAGZ,gBAAmC;AAAA,EAC5C,QAAiB,UAAU,IAAY;AAAA,EAEvC,aAAa;AACZ,WAAO,KAAK,QAAQ,UAAU;AAAA,EAC/B;AACD;", "names": []}