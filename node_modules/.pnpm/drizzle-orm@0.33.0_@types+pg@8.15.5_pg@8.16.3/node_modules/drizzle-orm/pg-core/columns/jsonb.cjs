"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var jsonb_exports = {};
__export(jsonb_exports, {
  PgJsonb: () => PgJsonb,
  PgJsonbBuilder: () => PgJsonbBuilder,
  jsonb: () => jsonb
});
module.exports = __toCommonJS(jsonb_exports);
var import_entity = require("../../entity.cjs");
var import_common = require("./common.cjs");
class PgJsonbBuilder extends import_common.PgColumnBuilder {
  static [import_entity.entityKind] = "PgJsonbBuilder";
  constructor(name) {
    super(name, "json", "PgJsonb");
  }
  /** @internal */
  build(table) {
    return new PgJsonb(table, this.config);
  }
}
class PgJsonb extends import_common.PgColumn {
  static [import_entity.entityKind] = "PgJsonb";
  constructor(table, config) {
    super(table, config);
  }
  getSQLType() {
    return "jsonb";
  }
  mapToDriverValue(value) {
    return JSON.stringify(value);
  }
  mapFromDriverValue(value) {
    if (typeof value === "string") {
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    }
    return value;
  }
}
function jsonb(name) {
  return new PgJsonbBuilder(name);
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  PgJsonb,
  PgJsonbBuilder,
  jsonb
});
//# sourceMappingURL=jsonb.cjs.map