{"version": 3, "sources": ["../../../src/pg-core/columns/bigint.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\n\nimport { PgColumn } from './common.ts';\nimport { PgIntColumnBaseBuilder } from './int.common.ts';\n\nexport type PgBigInt53BuilderInitial<TName extends string> = PgBigInt53Builder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'PgBigInt53';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class PgBigInt53Builder<T extends ColumnBuilderBaseConfig<'number', 'PgBigInt53'>>\n\textends PgIntColumnBaseBuilder<T>\n{\n\tstatic readonly [entityKind]: string = 'PgBigInt53Builder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'PgBigInt53');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgBigInt53<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgBigInt53<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgBigInt53<T extends ColumnBaseConfig<'number', 'PgBigInt53'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgBigInt53';\n\n\tgetSQLType(): string {\n\t\treturn 'bigint';\n\t}\n\n\toverride mapFromDriverValue(value: number | string): number {\n\t\tif (typeof value === 'number') {\n\t\t\treturn value;\n\t\t}\n\t\treturn Number(value);\n\t}\n}\n\nexport type PgBigInt64BuilderInitial<TName extends string> = PgBigInt64Builder<{\n\tname: TName;\n\tdataType: 'bigint';\n\tcolumnType: 'PgBigInt64';\n\tdata: bigint;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class PgBigInt64Builder<T extends ColumnBuilderBaseConfig<'bigint', 'PgBigInt64'>>\n\textends PgIntColumnBaseBuilder<T>\n{\n\tstatic readonly [entityKind]: string = 'PgBigInt64Builder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'bigint', 'PgBigInt64');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgBigInt64<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgBigInt64<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgBigInt64<T extends ColumnBaseConfig<'bigint', 'PgBigInt64'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgBigInt64';\n\n\tgetSQLType(): string {\n\t\treturn 'bigint';\n\t}\n\n\t// eslint-disable-next-line unicorn/prefer-native-coercion-functions\n\toverride mapFromDriverValue(value: string): bigint {\n\t\treturn BigInt(value);\n\t}\n}\n\ninterface PgBigIntConfig<T extends 'number' | 'bigint' = 'number' | 'bigint'> {\n\tmode: T;\n}\n\nexport function bigint<TName extends string, TMode extends PgBigIntConfig['mode']>(\n\tname: TName,\n\tconfig: PgBigIntConfig<TMode>,\n): TMode extends 'number' ? PgBigInt53BuilderInitial<TName> : PgBigInt64BuilderInitial<TName>;\nexport function bigint(name: string, config: PgBigIntConfig) {\n\tif (config.mode === 'number') {\n\t\treturn new PgBigInt53Builder(name);\n\t}\n\treturn new PgBigInt64Builder(name);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAG3B,oBAAyB;AACzB,wBAAuC;AAYhC,MAAM,0BACJ,yCACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,YAAY;AAAA,EACnC;AAAA;AAAA,EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI,WAA4C,OAAO,KAAK,MAA8C;AAAA,EAClH;AACD;AAEO,MAAM,mBAAuE,uBAAY;AAAA,EAC/F,QAAiB,wBAAU,IAAY;AAAA,EAEvC,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAgC;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO;AAAA,IACR;AACA,WAAO,OAAO,KAAK;AAAA,EACpB;AACD;AAYO,MAAM,0BACJ,yCACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,YAAY;AAAA,EACnC;AAAA;AAAA,EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,mBAAuE,uBAAY;AAAA,EAC/F,QAAiB,wBAAU,IAAY;AAAA,EAEvC,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA;AAAA,EAGS,mBAAmB,OAAuB;AAClD,WAAO,OAAO,KAAK;AAAA,EACpB;AACD;AAUO,SAAS,OAAO,MAAc,QAAwB;AAC5D,MAAI,OAAO,SAAS,UAAU;AAC7B,WAAO,IAAI,kBAAkB,IAAI;AAAA,EAClC;AACA,SAAO,IAAI,kBAAkB,IAAI;AAClC;", "names": []}