{"version": 3, "sources": ["../../../src/pg-core/columns/double-precision.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgDoublePrecisionBuilderInitial<TName extends string> = PgDoublePrecisionBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'PgDoublePrecision';\n\tdata: number;\n\tdriverParam: string | number;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class PgDoublePrecisionBuilder<T extends ColumnBuilderBaseConfig<'number', 'PgDoublePrecision'>>\n\textends PgColumnBuilder<T>\n{\n\tstatic readonly [entityKind]: string = 'PgDoublePrecisionBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'PgDoublePrecision');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgDoublePrecision<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgDoublePrecision<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgDoublePrecision<T extends ColumnBaseConfig<'number', 'PgDoublePrecision'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgDoublePrecision';\n\n\tgetSQLType(): string {\n\t\treturn 'double precision';\n\t}\n\n\toverride mapFromDriverValue(value: string | number): number {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number.parseFloat(value);\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function doublePrecision<TName extends string>(name: TName): PgDoublePrecisionBuilderInitial<TName> {\n\treturn new PgDoublePrecisionBuilder(name);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAA0C;AAYnC,MAAM,iCACJ,8BACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,mBAAmB;AAAA,EAC1C;AAAA;AAAA,EAGS,MACR,OACqD;AACrD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,0BAAqF,uBAAY;AAAA,EAC7G,QAAiB,wBAAU,IAAY;AAAA,EAEvC,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAgC;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,OAAO,WAAW,KAAK;AAAA,IAC/B;AACA,WAAO;AAAA,EACR;AACD;AAEO,SAAS,gBAAsC,MAAqD;AAC1G,SAAO,IAAI,yBAAyB,IAAI;AACzC;", "names": []}