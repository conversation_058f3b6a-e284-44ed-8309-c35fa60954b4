{"version": 3, "sources": ["../../../src/pg-core/columns/uuid.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { sql } from '~/sql/sql.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgUUIDBuilderInitial<TName extends string> = PgUUIDBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgUUID';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class PgUUIDBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgUUID'>> extends PgColumnBuilder<T> {\n\tstatic readonly [entityKind]: string = 'PgUUIDBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'PgUUID');\n\t}\n\n\t/**\n\t * Adds `default gen_random_uuid()` to the column definition.\n\t */\n\tdefaultRandom(): ReturnType<this['default']> {\n\t\treturn this.default(sql`gen_random_uuid()`) as ReturnType<this['default']>;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgUUID<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgUUID<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgUUID<T extends ColumnBaseConfig<'string', 'PgUUID'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgUUID';\n\n\tgetSQLType(): string {\n\t\treturn 'uuid';\n\t}\n}\n\nexport function uuid<TName extends string>(name: TName): PgUUIDBuilderInitial<TName> {\n\treturn new PgUUIDBuilder(name);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,WAAW;AACpB,SAAS,UAAU,uBAAuB;AAYnC,MAAM,sBAA6E,gBAAmB;AAAA,EAC5G,QAAiB,UAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,QAAQ;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAKA,gBAA6C;AAC5C,WAAO,KAAK,QAAQ,sBAAsB;AAAA,EAC3C;AAAA;AAAA,EAGS,MACR,OAC0C;AAC1C,WAAO,IAAI,OAAwC,OAAO,KAAK,MAA8C;AAAA,EAC9G;AACD;AAEO,MAAM,eAA+D,SAAY;AAAA,EACvF,QAAiB,UAAU,IAAY;AAAA,EAEvC,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAEO,SAAS,KAA2B,MAA0C;AACpF,SAAO,IAAI,cAAc,IAAI;AAC9B;", "names": []}