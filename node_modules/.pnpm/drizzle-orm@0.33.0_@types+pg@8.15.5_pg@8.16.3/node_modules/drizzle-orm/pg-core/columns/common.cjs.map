{"version": 3, "sources": ["../../../src/pg-core/columns/common.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBase,\n\tColumnBuilderBaseConfig,\n\tColumnBuilderExtraConfig,\n\tColumnBuilderRuntimeConfig,\n\tColumnDataType,\n\tGeneratedColumnConfig,\n\tHasGenerated,\n\tMakeColumnConfig,\n} from '~/column-builder.ts';\nimport { ColumnBuilder } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { Column } from '~/column.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport type { Update } from '~/utils.ts';\n\nimport type { SQL } from '~/index.ts';\nimport type { ForeignKey, UpdateDeleteAction } from '~/pg-core/foreign-keys.ts';\nimport { ForeignKeyBuilder } from '~/pg-core/foreign-keys.ts';\nimport type { AnyPgTable, PgTable } from '~/pg-core/table.ts';\nimport { iife } from '~/tracing-utils.ts';\nimport type { PgIndexOpClass } from '../indexes.ts';\nimport { uniqueKeyName } from '../unique-constraint.ts';\nimport { makePgArray, parsePgArray } from '../utils/array.ts';\n\nexport interface ReferenceConfig {\n\tref: () => PgColumn;\n\tactions: {\n\t\tonUpdate?: UpdateDeleteAction;\n\t\tonDelete?: UpdateDeleteAction;\n\t};\n}\n\nexport interface PgColumnBuilderBase<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTypeConfig extends object = object,\n> extends ColumnBuilderBase<T, TTypeConfig & { dialect: 'pg' }> {}\n\nexport abstract class PgColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTTypeConfig extends object = object,\n\tTExtraConfig extends ColumnBuilderExtraConfig = ColumnBuilderExtraConfig,\n> extends ColumnBuilder<T, TRuntimeConfig, TTypeConfig & { dialect: 'pg' }, TExtraConfig>\n\timplements PgColumnBuilderBase<T, TTypeConfig>\n{\n\tprivate foreignKeyConfigs: ReferenceConfig[] = [];\n\n\tstatic readonly [entityKind]: string = 'PgColumnBuilder';\n\n\tarray(size?: number): PgArrayBuilder<\n\t\t& {\n\t\t\tname: T['name'];\n\t\t\tdataType: 'array';\n\t\t\tcolumnType: 'PgArray';\n\t\t\tdata: T['data'][];\n\t\t\tdriverParam: T['driverParam'][] | string;\n\t\t\tenumValues: T['enumValues'];\n\t\t\tgenerated: GeneratedColumnConfig<T['data']>;\n\t\t}\n\t\t& (T extends { notNull: true } ? { notNull: true } : {})\n\t\t& (T extends { hasDefault: true } ? { hasDefault: true } : {}),\n\t\tT\n\t> {\n\t\treturn new PgArrayBuilder(this.config.name, this as PgColumnBuilder<any, any>, size);\n\t}\n\n\treferences(\n\t\tref: ReferenceConfig['ref'],\n\t\tactions: ReferenceConfig['actions'] = {},\n\t): this {\n\t\tthis.foreignKeyConfigs.push({ ref, actions });\n\t\treturn this;\n\t}\n\n\tunique(\n\t\tname?: string,\n\t\tconfig?: { nulls: 'distinct' | 'not distinct' },\n\t): this {\n\t\tthis.config.isUnique = true;\n\t\tthis.config.uniqueName = name;\n\t\tthis.config.uniqueType = config?.nulls;\n\t\treturn this;\n\t}\n\n\tgeneratedAlwaysAs(as: SQL | T['data'] | (() => SQL)): HasGenerated<this> {\n\t\tthis.config.generated = {\n\t\t\tas,\n\t\t\ttype: 'always',\n\t\t\tmode: 'stored',\n\t\t};\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tbuildForeignKeys(column: PgColumn, table: PgTable): ForeignKey[] {\n\t\treturn this.foreignKeyConfigs.map(({ ref, actions }) => {\n\t\t\treturn iife(\n\t\t\t\t(ref, actions) => {\n\t\t\t\t\tconst builder = new ForeignKeyBuilder(() => {\n\t\t\t\t\t\tconst foreignColumn = ref();\n\t\t\t\t\t\treturn { columns: [column], foreignColumns: [foreignColumn] };\n\t\t\t\t\t});\n\t\t\t\t\tif (actions.onUpdate) {\n\t\t\t\t\t\tbuilder.onUpdate(actions.onUpdate);\n\t\t\t\t\t}\n\t\t\t\t\tif (actions.onDelete) {\n\t\t\t\t\t\tbuilder.onDelete(actions.onDelete);\n\t\t\t\t\t}\n\t\t\t\t\treturn builder.build(table);\n\t\t\t\t},\n\t\t\t\tref,\n\t\t\t\tactions,\n\t\t\t);\n\t\t});\n\t}\n\n\t/** @internal */\n\tabstract build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgColumn<MakeColumnConfig<T, TTableName>>;\n\n\t/** @internal */\n\tbuildExtraConfigColumn<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): ExtraConfigColumn {\n\t\treturn new ExtraConfigColumn(table, this.config);\n\t}\n}\n\n// To understand how to use `PgColumn` and `PgColumn`, see `Column` and `AnyColumn` documentation.\nexport abstract class PgColumn<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = {},\n\tTTypeConfig extends object = {},\n> extends Column<T, TRuntimeConfig, TTypeConfig & { dialect: 'pg' }> {\n\tstatic readonly [entityKind]: string = 'PgColumn';\n\n\tconstructor(\n\t\toverride readonly table: PgTable,\n\t\tconfig: ColumnBuilderRuntimeConfig<T['data'], TRuntimeConfig>,\n\t) {\n\t\tif (!config.uniqueName) {\n\t\t\tconfig.uniqueName = uniqueKeyName(table, [config.name]);\n\t\t}\n\t\tsuper(table, config);\n\t}\n}\n\nexport type IndexedExtraConfigType = { order?: 'asc' | 'desc'; nulls?: 'first' | 'last'; opClass?: string };\n\nexport class ExtraConfigColumn<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n> extends PgColumn<T, IndexedExtraConfigType> {\n\tstatic readonly [entityKind]: string = 'ExtraConfigColumn';\n\n\toverride getSQLType(): string {\n\t\treturn this.getSQLType();\n\t}\n\n\tindexConfig: IndexedExtraConfigType = {\n\t\torder: this.config.order ?? 'asc',\n\t\tnulls: this.config.nulls ?? 'last',\n\t\topClass: this.config.opClass,\n\t};\n\tdefaultConfig: IndexedExtraConfigType = {\n\t\torder: 'asc',\n\t\tnulls: 'last',\n\t\topClass: undefined,\n\t};\n\n\tasc(): Omit<this, 'asc' | 'desc'> {\n\t\tthis.indexConfig.order = 'asc';\n\t\treturn this;\n\t}\n\n\tdesc(): Omit<this, 'asc' | 'desc'> {\n\t\tthis.indexConfig.order = 'desc';\n\t\treturn this;\n\t}\n\n\tnullsFirst(): Omit<this, 'nullsFirst' | 'nullsLast'> {\n\t\tthis.indexConfig.nulls = 'first';\n\t\treturn this;\n\t}\n\n\tnullsLast(): Omit<this, 'nullsFirst' | 'nullsLast'> {\n\t\tthis.indexConfig.nulls = 'last';\n\t\treturn this;\n\t}\n\n\t/**\n\t * ### PostgreSQL documentation quote\n\t *\n\t * > An operator class with optional parameters can be specified for each column of an index.\n\t * The operator class identifies the operators to be used by the index for that column.\n\t * For example, a B-tree index on four-byte integers would use the int4_ops class;\n\t * this operator class includes comparison functions for four-byte integers.\n\t * In practice the default operator class for the column's data type is usually sufficient.\n\t * The main point of having operator classes is that for some data types, there could be more than one meaningful ordering.\n\t * For example, we might want to sort a complex-number data type either by absolute value or by real part.\n\t * We could do this by defining two operator classes for the data type and then selecting the proper class when creating an index.\n\t * More information about operator classes check:\n\t *\n\t * ### Useful links\n\t * https://www.postgresql.org/docs/current/sql-createindex.html\n\t *\n\t * https://www.postgresql.org/docs/current/indexes-opclass.html\n\t *\n\t * https://www.postgresql.org/docs/current/xindex.html\n\t *\n\t * ### Additional types\n\t * If you have the `pg_vector` extension installed in your database, you can use the\n\t * `vector_l2_ops`, `vector_ip_ops`, `vector_cosine_ops`, `vector_l1_ops`, `bit_hamming_ops`, `bit_jaccard_ops`, `halfvec_l2_ops`, `sparsevec_l2_ops` options, which are predefined types.\n\t *\n\t * **You can always specify any string you want in the operator class, in case Drizzle doesn't have it natively in its types**\n\t *\n\t * @param opClass\n\t * @returns\n\t */\n\top(opClass: PgIndexOpClass): Omit<this, 'op'> {\n\t\tthis.indexConfig.opClass = opClass;\n\t\treturn this;\n\t}\n}\n\nexport class IndexedColumn {\n\tstatic readonly [entityKind]: string = 'IndexedColumn';\n\tconstructor(\n\t\tname: string | undefined,\n\t\ttype: string,\n\t\tindexConfig: IndexedExtraConfigType,\n\t) {\n\t\tthis.name = name;\n\t\tthis.type = type;\n\t\tthis.indexConfig = indexConfig;\n\t}\n\n\tname: string | undefined;\n\ttype: string;\n\tindexConfig: IndexedExtraConfigType;\n}\n\nexport type AnyPgColumn<TPartial extends Partial<ColumnBaseConfig<ColumnDataType, string>> = {}> = PgColumn<\n\tRequired<Update<ColumnBaseConfig<ColumnDataType, string>, TPartial>>\n>;\n\nexport class PgArrayBuilder<\n\tT extends ColumnBuilderBaseConfig<'array', 'PgArray'>,\n\tTBase extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n> extends PgColumnBuilder<\n\tT,\n\t{\n\t\tbaseBuilder: PgColumnBuilder<TBase>;\n\t\tsize: number | undefined;\n\t},\n\t{\n\t\tbaseBuilder: PgColumnBuilder<TBase>;\n\t}\n> {\n\tstatic override readonly [entityKind] = 'PgArrayBuilder';\n\n\tconstructor(\n\t\tname: string,\n\t\tbaseBuilder: PgArrayBuilder<T, TBase>['config']['baseBuilder'],\n\t\tsize: number | undefined,\n\t) {\n\t\tsuper(name, 'array', 'PgArray');\n\t\tthis.config.baseBuilder = baseBuilder;\n\t\tthis.config.size = size;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgArray<MakeColumnConfig<T, TTableName>, TBase> {\n\t\tconst baseColumn = this.config.baseBuilder.build(table);\n\t\treturn new PgArray<MakeColumnConfig<T, TTableName>, TBase>(\n\t\t\ttable as AnyPgTable<{ name: MakeColumnConfig<T, TTableName>['tableName'] }>,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t\tbaseColumn,\n\t\t);\n\t}\n}\n\nexport class PgArray<\n\tT extends ColumnBaseConfig<'array', 'PgArray'>,\n\tTBase extends ColumnBuilderBaseConfig<ColumnDataType, string>,\n> extends PgColumn<T> {\n\treadonly size: number | undefined;\n\n\tstatic readonly [entityKind]: string = 'PgArray';\n\n\tconstructor(\n\t\ttable: AnyPgTable<{ name: T['tableName'] }>,\n\t\tconfig: PgArrayBuilder<T, TBase>['config'],\n\t\treadonly baseColumn: PgColumn,\n\t\treadonly range?: [number | undefined, number | undefined],\n\t) {\n\t\tsuper(table, config);\n\t\tthis.size = config.size;\n\t}\n\n\tgetSQLType(): string {\n\t\treturn `${this.baseColumn.getSQLType()}[${typeof this.size === 'number' ? this.size : ''}]`;\n\t}\n\n\toverride mapFromDriverValue(value: unknown[] | string): T['data'] {\n\t\tif (typeof value === 'string') {\n\t\t\t// Thank you node-postgres for not parsing enum arrays\n\t\t\tvalue = parsePgArray(value);\n\t\t}\n\t\treturn value.map((v) => this.baseColumn.mapFromDriverValue(v));\n\t}\n\n\toverride mapToDriverValue(value: unknown[], isNestedArray = false): unknown[] | string {\n\t\tconst a = value.map((v) =>\n\t\t\tv === null\n\t\t\t\t? null\n\t\t\t\t: is(this.baseColumn, PgArray)\n\t\t\t\t? this.baseColumn.mapToDriverValue(v as unknown[], true)\n\t\t\t\t: this.baseColumn.mapToDriverValue(v)\n\t\t);\n\t\tif (isNestedArray) return a;\n\t\treturn makePgArray(a);\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,4BAA8B;AAE9B,oBAAuB;AACvB,oBAA+B;AAK/B,0BAAkC;AAElC,2BAAqB;AAErB,+BAA8B;AAC9B,mBAA0C;AAenC,MAAe,wBAKZ,oCAEV;AAAA,EACS,oBAAuC,CAAC;AAAA,EAEhD,QAAiB,wBAAU,IAAY;AAAA,EAEvC,MAAM,MAaJ;AACD,WAAO,IAAI,eAAe,KAAK,OAAO,MAAM,MAAmC,IAAI;AAAA,EACpF;AAAA,EAEA,WACC,KACA,UAAsC,CAAC,GAChC;AACP,SAAK,kBAAkB,KAAK,EAAE,KAAK,QAAQ,CAAC;AAC5C,WAAO;AAAA,EACR;AAAA,EAEA,OACC,MACA,QACO;AACP,SAAK,OAAO,WAAW;AACvB,SAAK,OAAO,aAAa;AACzB,SAAK,OAAO,aAAa,QAAQ;AACjC,WAAO;AAAA,EACR;AAAA,EAEA,kBAAkB,IAAuD;AACxE,SAAK,OAAO,YAAY;AAAA,MACvB;AAAA,MACA,MAAM;AAAA,MACN,MAAM;AAAA,IACP;AACA,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,iBAAiB,QAAkB,OAA8B;AAChE,WAAO,KAAK,kBAAkB,IAAI,CAAC,EAAE,KAAK,QAAQ,MAAM;AACvD,iBAAO;AAAA,QACN,CAACA,MAAKC,aAAY;AACjB,gBAAM,UAAU,IAAI,sCAAkB,MAAM;AAC3C,kBAAM,gBAAgBD,KAAI;AAC1B,mBAAO,EAAE,SAAS,CAAC,MAAM,GAAG,gBAAgB,CAAC,aAAa,EAAE;AAAA,UAC7D,CAAC;AACD,cAAIC,SAAQ,UAAU;AACrB,oBAAQ,SAASA,SAAQ,QAAQ;AAAA,UAClC;AACA,cAAIA,SAAQ,UAAU;AACrB,oBAAQ,SAASA,SAAQ,QAAQ;AAAA,UAClC;AACA,iBAAO,QAAQ,MAAM,KAAK;AAAA,QAC3B;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA,EAQA,uBACC,OACoB;AACpB,WAAO,IAAI,kBAAkB,OAAO,KAAK,MAAM;AAAA,EAChD;AACD;AAGO,MAAe,iBAIZ,qBAA2D;AAAA,EAGpE,YACmB,OAClB,QACC;AACD,QAAI,CAAC,OAAO,YAAY;AACvB,aAAO,iBAAa,wCAAc,OAAO,CAAC,OAAO,IAAI,CAAC;AAAA,IACvD;AACA,UAAM,OAAO,MAAM;AAND;AAAA,EAOnB;AAAA,EAVA,QAAiB,wBAAU,IAAY;AAWxC;AAIO,MAAM,0BAEH,SAAoC;AAAA,EAC7C,QAAiB,wBAAU,IAAY;AAAA,EAE9B,aAAqB;AAC7B,WAAO,KAAK,WAAW;AAAA,EACxB;AAAA,EAEA,cAAsC;AAAA,IACrC,OAAO,KAAK,OAAO,SAAS;AAAA,IAC5B,OAAO,KAAK,OAAO,SAAS;AAAA,IAC5B,SAAS,KAAK,OAAO;AAAA,EACtB;AAAA,EACA,gBAAwC;AAAA,IACvC,OAAO;AAAA,IACP,OAAO;AAAA,IACP,SAAS;AAAA,EACV;AAAA,EAEA,MAAkC;AACjC,SAAK,YAAY,QAAQ;AACzB,WAAO;AAAA,EACR;AAAA,EAEA,OAAmC;AAClC,SAAK,YAAY,QAAQ;AACzB,WAAO;AAAA,EACR;AAAA,EAEA,aAAqD;AACpD,SAAK,YAAY,QAAQ;AACzB,WAAO;AAAA,EACR;AAAA,EAEA,YAAoD;AACnD,SAAK,YAAY,QAAQ;AACzB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+BA,GAAG,SAA2C;AAC7C,SAAK,YAAY,UAAU;AAC3B,WAAO;AAAA,EACR;AACD;AAEO,MAAM,cAAc;AAAA,EAC1B,QAAiB,wBAAU,IAAY;AAAA,EACvC,YACC,MACA,MACA,aACC;AACD,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,cAAc;AAAA,EACpB;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AACD;AAMO,MAAM,uBAGH,gBASR;AAAA,EACD,QAA0B,wBAAU,IAAI;AAAA,EAExC,YACC,MACA,aACA,MACC;AACD,UAAM,MAAM,SAAS,SAAS;AAC9B,SAAK,OAAO,cAAc;AAC1B,SAAK,OAAO,OAAO;AAAA,EACpB;AAAA;AAAA,EAGS,MACR,OACkD;AAClD,UAAM,aAAa,KAAK,OAAO,YAAY,MAAM,KAAK;AACtD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,MACL;AAAA,IACD;AAAA,EACD;AACD;AAEO,MAAM,gBAGH,SAAY;AAAA,EAKrB,YACC,OACA,QACS,YACA,OACR;AACD,UAAM,OAAO,MAAM;AAHV;AACA;AAGT,SAAK,OAAO,OAAO;AAAA,EACpB;AAAA,EAZS;AAAA,EAET,QAAiB,wBAAU,IAAY;AAAA,EAYvC,aAAqB;AACpB,WAAO,GAAG,KAAK,WAAW,WAAW,CAAC,IAAI,OAAO,KAAK,SAAS,WAAW,KAAK,OAAO,EAAE;AAAA,EACzF;AAAA,EAES,mBAAmB,OAAsC;AACjE,QAAI,OAAO,UAAU,UAAU;AAE9B,kBAAQ,2BAAa,KAAK;AAAA,IAC3B;AACA,WAAO,MAAM,IAAI,CAAC,MAAM,KAAK,WAAW,mBAAmB,CAAC,CAAC;AAAA,EAC9D;AAAA,EAES,iBAAiB,OAAkB,gBAAgB,OAA2B;AACtF,UAAM,IAAI,MAAM;AAAA,MAAI,CAAC,MACpB,MAAM,OACH,WACA,kBAAG,KAAK,YAAY,OAAO,IAC3B,KAAK,WAAW,iBAAiB,GAAgB,IAAI,IACrD,KAAK,WAAW,iBAAiB,CAAC;AAAA,IACtC;AACA,QAAI;AAAe,aAAO;AAC1B,eAAO,0BAAY,CAAC;AAAA,EACrB;AACD;", "names": ["ref", "actions"]}