{"version": 3, "sources": ["../../../src/pg-core/columns/char.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport type { Writable } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgCharBuilderInitial<TName extends string, TEnum extends [string, ...string[]]> = PgCharBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgChar';\n\tdata: TEnum[number];\n\tenumValues: TEnum;\n\tdriverParam: string;\n\tgenerated: undefined;\n}>;\n\nexport class PgCharBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgChar'>> extends PgColumnBuilder<\n\tT,\n\t{ length: number | undefined; enumValues: T['enumValues'] }\n> {\n\tstatic readonly [entityKind]: string = 'PgCharBuilder';\n\n\tconstructor(name: string, config: PgCharConfig<T['enumValues']>) {\n\t\tsuper(name, 'string', 'PgChar');\n\t\tthis.config.length = config.length;\n\t\tthis.config.enumValues = config.enum;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgChar<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgChar<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgChar<T extends ColumnBaseConfig<'string', 'PgChar'>>\n\textends PgColumn<T, { length: number | undefined; enumValues: T['enumValues'] }>\n{\n\tstatic readonly [entityKind]: string = 'PgChar';\n\n\treadonly length = this.config.length;\n\toverride readonly enumValues = this.config.enumValues;\n\n\tgetSQLType(): string {\n\t\treturn this.length === undefined ? `char` : `char(${this.length})`;\n\t}\n}\n\nexport interface PgCharConfig<TEnum extends readonly string[] | string[] | undefined> {\n\tlength?: number;\n\tenum?: TEnum;\n}\n\nexport function char<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tconfig: PgCharConfig<T | Writable<T>> = {},\n): PgCharBuilderInitial<TName, Writable<T>> {\n\treturn new PgCharBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAG3B,oBAA0C;AAYnC,MAAM,sBAA6E,8BAGxF;AAAA,EACD,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAc,QAAuC;AAChE,UAAM,MAAM,UAAU,QAAQ;AAC9B,SAAK,OAAO,SAAS,OAAO;AAC5B,SAAK,OAAO,aAAa,OAAO;AAAA,EACjC;AAAA;AAAA,EAGS,MACR,OAC0C;AAC1C,WAAO,IAAI,OAAwC,OAAO,KAAK,MAA8C;AAAA,EAC9G;AACD;AAEO,MAAM,eACJ,uBACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAE9B,SAAS,KAAK,OAAO;AAAA,EACZ,aAAa,KAAK,OAAO;AAAA,EAE3C,aAAqB;AACpB,WAAO,KAAK,WAAW,SAAY,SAAS,QAAQ,KAAK,MAAM;AAAA,EAChE;AACD;AAOO,SAAS,KACf,MACA,SAAwC,CAAC,GACE;AAC3C,SAAO,IAAI,cAAc,MAAM,MAAM;AACtC;", "names": []}