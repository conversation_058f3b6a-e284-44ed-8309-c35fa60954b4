{"version": 3, "sources": ["../../../src/pg-core/columns/enum.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport type { Writable } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgEnumColumnBuilderInitial<TName extends string, TValues extends [string, ...string[]]> =\n\tPgEnumColumnBuilder<{\n\t\tname: TName;\n\t\tdataType: 'string';\n\t\tcolumnType: 'PgEnumColumn';\n\t\tdata: TValues[number];\n\t\tenumValues: TValues;\n\t\tdriverParam: string;\n\t\tgenerated: undefined;\n\t}>;\n\nconst isPgEnumSym = Symbol.for('drizzle:isPgEnum');\nexport interface PgEnum<TValues extends [string, ...string[]]> {\n\t<TName extends string>(name: TName): PgEnumColumnBuilderInitial<TName, TValues>;\n\n\treadonly enumName: string;\n\treadonly enumValues: TValues;\n\treadonly schema: string | undefined;\n\t/** @internal */\n\t[isPgEnumSym]: true;\n}\n\nexport function isPgEnum(obj: unknown): obj is PgEnum<[string, ...string[]]> {\n\treturn !!obj && typeof obj === 'function' && isPgEnumSym in obj && obj[isPgEnumSym] === true;\n}\n\nexport class PgEnumColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'PgEnumColumn'> & { enumValues: [string, ...string[]] },\n> extends PgColumnBuilder<T, { enum: PgEnum<T['enumValues']> }> {\n\tstatic readonly [entityKind]: string = 'PgEnumColumnBuilder';\n\n\tconstructor(name: string, enumInstance: PgEnum<T['enumValues']>) {\n\t\tsuper(name, 'string', 'PgEnumColumn');\n\t\tthis.config.enum = enumInstance;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgEnumColumn<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgEnumColumn<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgEnumColumn<T extends ColumnBaseConfig<'string', 'PgEnumColumn'> & { enumValues: [string, ...string[]] }>\n\textends PgColumn<T, { enum: PgEnum<T['enumValues']> }>\n{\n\tstatic readonly [entityKind]: string = 'PgEnumColumn';\n\n\treadonly enum = this.config.enum;\n\toverride readonly enumValues = this.config.enum.enumValues;\n\n\tconstructor(\n\t\ttable: AnyPgTable<{ name: T['tableName'] }>,\n\t\tconfig: PgEnumColumnBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t\tthis.enum = config.enum;\n\t}\n\n\tgetSQLType(): string {\n\t\treturn this.enum.enumName;\n\t}\n}\n\n// Gratitude to zod for the enum function types\nexport function pgEnum<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tenumName: string,\n\tvalues: T | Writable<T>,\n): PgEnum<Writable<T>> {\n\treturn pgEnumWithSchema(enumName, values, undefined);\n}\n\n/** @internal */\nexport function pgEnumWithSchema<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tenumName: string,\n\tvalues: T | Writable<T>,\n\tschema?: string,\n): PgEnum<Writable<T>> {\n\tconst enumInstance: PgEnum<Writable<T>> = Object.assign(\n\t\t<TName extends string>(name: TName): PgEnumColumnBuilderInitial<TName, Writable<T>> =>\n\t\t\tnew PgEnumColumnBuilder(name, enumInstance),\n\t\t{\n\t\t\tenumName,\n\t\t\tenumValues: values,\n\t\t\tschema,\n\t\t\t[isPgEnumSym]: true,\n\t\t} as const,\n\t);\n\n\treturn enumInstance;\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAG3B,SAAS,UAAU,uBAAuB;AAa1C,MAAM,cAAc,OAAO,IAAI,kBAAkB;AAW1C,SAAS,SAAS,KAAoD;AAC5E,SAAO,CAAC,CAAC,OAAO,OAAO,QAAQ,cAAc,eAAe,OAAO,IAAI,WAAW,MAAM;AACzF;AAEO,MAAM,4BAEH,gBAAsD;AAAA,EAC/D,QAAiB,UAAU,IAAY;AAAA,EAEvC,YAAY,MAAc,cAAuC;AAChE,UAAM,MAAM,UAAU,cAAc;AACpC,SAAK,OAAO,OAAO;AAAA,EACpB;AAAA;AAAA,EAGS,MACR,OACgD;AAChD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,qBACJ,SACT;AAAA,EACC,QAAiB,UAAU,IAAY;AAAA,EAE9B,OAAO,KAAK,OAAO;AAAA,EACV,aAAa,KAAK,OAAO,KAAK;AAAA,EAEhD,YACC,OACA,QACC;AACD,UAAM,OAAO,MAAM;AACnB,SAAK,OAAO,OAAO;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO,KAAK,KAAK;AAAA,EAClB;AACD;AAGO,SAAS,OACf,UACA,QACsB;AACtB,SAAO,iBAAiB,UAAU,QAAQ,MAAS;AACpD;AAGO,SAAS,iBACf,UACA,QACA,QACsB;AACtB,QAAM,eAAoC,OAAO;AAAA,IAChD,CAAuB,SACtB,IAAI,oBAAoB,MAAM,YAAY;AAAA,IAC3C;AAAA,MACC;AAAA,MACA,YAAY;AAAA,MACZ;AAAA,MACA,CAAC,WAAW,GAAG;AAAA,IAChB;AAAA,EACD;AAEA,SAAO;AACR;", "names": []}