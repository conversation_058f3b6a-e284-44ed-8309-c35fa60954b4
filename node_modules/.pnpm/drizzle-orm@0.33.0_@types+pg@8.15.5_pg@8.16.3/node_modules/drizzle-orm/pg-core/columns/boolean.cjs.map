{"version": 3, "sources": ["../../../src/pg-core/columns/boolean.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgBooleanBuilderInitial<TName extends string> = PgBooleanBuilder<{\n\tname: TName;\n\tdataType: 'boolean';\n\tcolumnType: 'PgBoolean';\n\tdata: boolean;\n\tdriverParam: boolean;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class PgBooleanBuilder<T extends ColumnBuilderBaseConfig<'boolean', 'PgBoolean'>> extends PgColumnBuilder<T> {\n\tstatic readonly [entityKind]: string = 'PgBooleanBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'boolean', 'PgBoolean');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgBoolean<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgBoolean<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgBoolean<T extends ColumnBaseConfig<'boolean', 'PgBoolean'>> extends PgColumn<T> {\n\tstatic readonly [entityKind]: string = 'PgBoolean';\n\n\tgetSQLType(): string {\n\t\treturn 'boolean';\n\t}\n}\n\nexport function boolean<TName extends string>(name: TName): PgBooleanBuilderInitial<TName> {\n\treturn new PgBooleanBuilder(name);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAA0C;AAYnC,MAAM,yBAAoF,8BAAmB;AAAA,EACnH,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAiB;AAC5B,UAAM,MAAM,WAAW,WAAW;AAAA,EACnC;AAAA;AAAA,EAGS,MACR,OAC6C;AAC7C,WAAO,IAAI,UAA2C,OAAO,KAAK,MAA8C;AAAA,EACjH;AACD;AAEO,MAAM,kBAAsE,uBAAY;AAAA,EAC9F,QAAiB,wBAAU,IAAY;AAAA,EAEvC,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAEO,SAAS,QAA8B,MAA6C;AAC1F,SAAO,IAAI,iBAAiB,IAAI;AACjC;", "names": []}