{"version": 3, "sources": ["../../../../src/pg-core/columns/vector_extension/bit.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from '../common.ts';\n\nexport type PgBinaryVectorBuilderInitial<TName extends string> = PgBinaryVectorBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgBinaryVector';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class PgBinaryVectorBuilder<T extends ColumnBuilderBaseConfig<'string', 'PgBinaryVector'>>\n\textends PgColumnBuilder<\n\t\tT,\n\t\t{ dimensions: number | undefined }\n\t>\n{\n\tstatic readonly [entityKind]: string = 'PgBinaryVectorBuilder';\n\n\tconstructor(name: string, config: PgBinaryVectorConfig) {\n\t\tsuper(name, 'string', 'PgBinaryVector');\n\t\tthis.config.dimensions = config.dimensions;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgBinaryVector<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgBinaryVector<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgBinaryVector<T extends ColumnBaseConfig<'string', 'PgBinaryVector'>>\n\textends PgColumn<T, { dimensions: number | undefined }>\n{\n\tstatic readonly [entityKind]: string = 'PgBinaryVector';\n\n\treadonly dimensions = this.config.dimensions;\n\n\tgetSQLType(): string {\n\t\treturn `bit(${this.dimensions})`;\n\t}\n}\n\nexport interface PgBinaryVectorConfig {\n\tdimensions: number;\n}\n\nexport function bit<TName extends string>(\n\tname: TName,\n\tconfig: PgBinaryVectorConfig,\n): PgBinaryVectorBuilderInitial<TName> {\n\treturn new PgBinaryVectorBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAA0C;AAYnC,MAAM,8BACJ,8BAIT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAc,QAA8B;AACvD,UAAM,MAAM,UAAU,gBAAgB;AACtC,SAAK,OAAO,aAAa,OAAO;AAAA,EACjC;AAAA;AAAA,EAGS,MACR,OACkD;AAClD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,uBACJ,uBACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAE9B,aAAa,KAAK,OAAO;AAAA,EAElC,aAAqB;AACpB,WAAO,OAAO,KAAK,UAAU;AAAA,EAC9B;AACD;AAMO,SAAS,IACf,MACA,QACsC;AACtC,SAAO,IAAI,sBAAsB,MAAM,MAAM;AAC9C;", "names": []}