{"version": 3, "sources": ["../../../../src/pg-core/columns/vector_extension/vector.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from '../common.ts';\n\nexport type PgVectorBuilderInitial<TName extends string> = PgVectorBuilder<{\n\tname: TName;\n\tdataType: 'array';\n\tcolumnType: 'PgVector';\n\tdata: number[];\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class PgVectorBuilder<T extends ColumnBuilderBaseConfig<'array', 'PgVector'>> extends PgColumnBuilder<\n\tT,\n\t{ dimensions: number | undefined }\n> {\n\tstatic readonly [entityKind]: string = 'PgVectorBuilder';\n\n\tconstructor(name: string, config: PgVectorConfig) {\n\t\tsuper(name, 'array', 'PgVector');\n\t\tthis.config.dimensions = config.dimensions;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgVector<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgVector<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgVector<T extends ColumnBaseConfig<'array', 'PgVector'>>\n\textends PgColumn<T, { dimensions: number | undefined }>\n{\n\tstatic readonly [entityKind]: string = 'PgVector';\n\n\treadonly dimensions = this.config.dimensions;\n\n\tgetSQLType(): string {\n\t\treturn `vector(${this.dimensions})`;\n\t}\n\n\toverride mapToDriverValue(value: unknown): unknown {\n\t\treturn JSON.stringify(value);\n\t}\n\n\toverride mapFromDriverValue(value: string): unknown {\n\t\treturn value\n\t\t\t.slice(1, -1)\n\t\t\t.split(',')\n\t\t\t.map((v) => Number.parseFloat(v));\n\t}\n}\n\nexport interface PgVectorConfig {\n\tdimensions: number;\n}\n\nexport function vector<TName extends string>(\n\tname: TName,\n\tconfig: PgVectorConfig,\n): PgVectorBuilderInitial<TName> {\n\treturn new PgVectorBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAA0C;AAYnC,MAAM,wBAAgF,8BAG3F;AAAA,EACD,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAc,QAAwB;AACjD,UAAM,MAAM,SAAS,UAAU;AAC/B,SAAK,OAAO,aAAa,OAAO;AAAA,EACjC;AAAA;AAAA,EAGS,MACR,OAC4C;AAC5C,WAAO,IAAI,SAA0C,OAAO,KAAK,MAA8C;AAAA,EAChH;AACD;AAEO,MAAM,iBACJ,uBACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAE9B,aAAa,KAAK,OAAO;AAAA,EAElC,aAAqB;AACpB,WAAO,UAAU,KAAK,UAAU;AAAA,EACjC;AAAA,EAES,iBAAiB,OAAyB;AAClD,WAAO,KAAK,UAAU,KAAK;AAAA,EAC5B;AAAA,EAES,mBAAmB,OAAwB;AACnD,WAAO,MACL,MAAM,GAAG,EAAE,EACX,MAAM,GAAG,EACT,IAAI,CAAC,MAAM,OAAO,WAAW,CAAC,CAAC;AAAA,EAClC;AACD;AAMO,SAAS,OACf,MACA,QACgC;AAChC,SAAO,IAAI,gBAAgB,MAAM,MAAM;AACxC;", "names": []}