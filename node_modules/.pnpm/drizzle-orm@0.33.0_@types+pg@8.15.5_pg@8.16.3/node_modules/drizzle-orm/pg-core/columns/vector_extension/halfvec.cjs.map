{"version": 3, "sources": ["../../../../src/pg-core/columns/vector_extension/halfvec.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from '../common.ts';\n\nexport type PgHalfVectorBuilderInitial<TName extends string> = PgHalfVectorBuilder<{\n\tname: TName;\n\tdataType: 'array';\n\tcolumnType: 'PgHalfVector';\n\tdata: number[];\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class PgHalfVectorBuilder<T extends ColumnBuilderBaseConfig<'array', 'PgHalfVector'>> extends PgColumnBuilder<\n\tT,\n\t{ dimensions: number | undefined }\n> {\n\tstatic readonly [entityKind]: string = 'PgHalfVectorBuilder';\n\n\tconstructor(name: string, config: PgHalfVectorConfig) {\n\t\tsuper(name, 'array', 'PgHalfVector');\n\t\tthis.config.dimensions = config.dimensions;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgHalfVector<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgHalfVector<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgHalfVector<T extends ColumnBaseConfig<'array', 'PgHalfVector'>>\n\textends PgColumn<T, { dimensions: number | undefined }>\n{\n\tstatic readonly [entityKind]: string = 'PgHalfVector';\n\n\treadonly dimensions = this.config.dimensions;\n\n\tgetSQLType(): string {\n\t\treturn `halfvec(${this.dimensions})`;\n\t}\n\n\toverride mapToDriverValue(value: unknown): unknown {\n\t\treturn JSON.stringify(value);\n\t}\n\n\toverride mapFromDriverValue(value: string): unknown {\n\t\treturn value\n\t\t\t.slice(1, -1)\n\t\t\t.split(',')\n\t\t\t.map((v) => Number.parseFloat(v));\n\t}\n}\n\nexport interface PgHalfVectorConfig {\n\tdimensions: number;\n}\n\nexport function halfvec<TName extends string>(\n\tname: TName,\n\tconfig: PgHalfVectorConfig,\n): PgHalfVectorBuilderInitial<TName> {\n\treturn new PgHalfVectorBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAA0C;AAYnC,MAAM,4BAAwF,8BAGnG;AAAA,EACD,QAAiB,wBAAU,IAAY;AAAA,EAEvC,YAAY,MAAc,QAA4B;AACrD,UAAM,MAAM,SAAS,cAAc;AACnC,SAAK,OAAO,aAAa,OAAO;AAAA,EACjC;AAAA;AAAA,EAGS,MACR,OACgD;AAChD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,qBACJ,uBACT;AAAA,EACC,QAAiB,wBAAU,IAAY;AAAA,EAE9B,aAAa,KAAK,OAAO;AAAA,EAElC,aAAqB;AACpB,WAAO,WAAW,KAAK,UAAU;AAAA,EAClC;AAAA,EAES,iBAAiB,OAAyB;AAClD,WAAO,KAAK,UAAU,KAAK;AAAA,EAC5B;AAAA,EAES,mBAAmB,OAAwB;AACnD,WAAO,MACL,MAAM,GAAG,EAAE,EACX,MAAM,GAAG,EACT,IAAI,CAAC,MAAM,OAAO,WAAW,CAAC,CAAC;AAAA,EAClC;AACD;AAMO,SAAS,QACf,MACA,QACoC;AACpC,SAAO,IAAI,oBAAoB,MAAM,MAAM;AAC5C;", "names": []}