{"version": 3, "sources": ["../../src/neon-serverless/driver.ts"], "sourcesContent": ["import { types } from '@neondatabase/serverless';\nimport { entityKind } from '~/entity.ts';\nimport type { Logger } from '~/logger.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport { PgDatabase } from '~/pg-core/db.ts';\nimport { PgDialect } from '~/pg-core/dialect.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport type { NeonClient, NeonQueryResultHKT } from './session.ts';\nimport { NeonSession } from './session.ts';\n\nexport interface NeonDriverOptions {\n\tlogger?: Logger;\n}\n\nexport class NeonDriver {\n\tstatic readonly [entityKind]: string = 'NeonDriver';\n\n\tconstructor(\n\t\tprivate client: NeonClient,\n\t\tprivate dialect: PgDialect,\n\t\tprivate options: NeonDriverOptions = {},\n\t) {\n\t\tthis.initMappers();\n\t}\n\n\tcreateSession(\n\t\tschema: RelationalSchemaConfig<TablesRelationalConfig> | undefined,\n\t): NeonSession<Record<string, unknown>, TablesRelationalConfig> {\n\t\treturn new NeonSession(this.client, this.dialect, schema, { logger: this.options.logger });\n\t}\n\n\tinitMappers() {\n\t\ttypes.setTypeParser(types.builtins.TIMESTAMPTZ, (val) => val);\n\t\ttypes.setTypeParser(types.builtins.TIMESTAMP, (val) => val);\n\t\ttypes.setTypeParser(types.builtins.DATE, (val) => val);\n\t\ttypes.setTypeParser(types.builtins.INTERVAL, (val) => val);\n\t}\n}\n\nexport type NeonDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> = PgDatabase<NeonQueryResultHKT, TSchema>;\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: NeonClient,\n\tconfig: DrizzleConfig<TSchema> = {},\n): NeonDatabase<TSchema> {\n\tconst dialect = new PgDialect();\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst driver = new NeonDriver(client, dialect, { logger });\n\tconst session = driver.createSession(schema);\n\treturn new PgDatabase(dialect, session, schema) as NeonDatabase<TSchema>;\n}\n"], "mappings": "AAAA,SAAS,aAAa;AACtB,SAAS,kBAAkB;AAE3B,SAAS,qBAAqB;AAC9B,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;AAC1B;AAAA,EACC;AAAA,EACA;AAAA,OAGM;AAGP,SAAS,mBAAmB;AAMrB,MAAM,WAAW;AAAA,EAGvB,YACS,QACA,SACA,UAA6B,CAAC,GACrC;AAHO;AACA;AACA;AAER,SAAK,YAAY;AAAA,EAClB;AAAA,EARA,QAAiB,UAAU,IAAY;AAAA,EAUvC,cACC,QAC+D;AAC/D,WAAO,IAAI,YAAY,KAAK,QAAQ,KAAK,SAAS,QAAQ,EAAE,QAAQ,KAAK,QAAQ,OAAO,CAAC;AAAA,EAC1F;AAAA,EAEA,cAAc;AACb,UAAM,cAAc,MAAM,SAAS,aAAa,CAAC,QAAQ,GAAG;AAC5D,UAAM,cAAc,MAAM,SAAS,WAAW,CAAC,QAAQ,GAAG;AAC1D,UAAM,cAAc,MAAM,SAAS,MAAM,CAAC,QAAQ,GAAG;AACrD,UAAM,cAAc,MAAM,SAAS,UAAU,CAAC,QAAQ,GAAG;AAAA,EAC1D;AACD;AAMO,SAAS,QACf,QACA,SAAiC,CAAC,GACV;AACxB,QAAM,UAAU,IAAI,UAAU;AAC9B,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,eAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,SAAS,IAAI,WAAW,QAAQ,SAAS,EAAE,OAAO,CAAC;AACzD,QAAM,UAAU,OAAO,cAAc,MAAM;AAC3C,SAAO,IAAI,WAAW,SAAS,SAAS,MAAM;AAC/C;", "names": []}