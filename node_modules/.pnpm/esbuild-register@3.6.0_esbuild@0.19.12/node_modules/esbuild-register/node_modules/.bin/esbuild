#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/TraeProjects/stock-ifind/node_modules/.pnpm/esbuild@0.19.12/node_modules/esbuild/bin/node_modules:/Users/<USER>/TraeProjects/stock-ifind/node_modules/.pnpm/esbuild@0.19.12/node_modules/esbuild/node_modules:/Users/<USER>/TraeProjects/stock-ifind/node_modules/.pnpm/esbuild@0.19.12/node_modules:/Users/<USER>/TraeProjects/stock-ifind/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/TraeProjects/stock-ifind/node_modules/.pnpm/esbuild@0.19.12/node_modules/esbuild/bin/node_modules:/Users/<USER>/TraeProjects/stock-ifind/node_modules/.pnpm/esbuild@0.19.12/node_modules/esbuild/node_modules:/Users/<USER>/TraeProjects/stock-ifind/node_modules/.pnpm/esbuild@0.19.12/node_modules:/Users/<USER>/TraeProjects/stock-ifind/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../esbuild@0.19.12/node_modules/esbuild/bin/esbuild" "$@"
else
  exec node  "$basedir/../../../../../esbuild@0.19.12/node_modules/esbuild/bin/esbuild" "$@"
fi
