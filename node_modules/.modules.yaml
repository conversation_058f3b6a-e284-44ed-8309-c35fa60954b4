hoistPattern:
  - '*'
hoistedDependencies:
  '@colors/colors@1.6.0':
    '@colors/colors': private
  '@dabh/diagnostics@2.0.3':
    '@dabh/diagnostics': private
  '@drizzle-team/brocli@0.10.2':
    '@drizzle-team/brocli': private
  '@esbuild-kit/core-utils@3.3.2':
    '@esbuild-kit/core-utils': private
  '@esbuild-kit/esm-loader@2.6.5':
    '@esbuild-kit/esm-loader': private
  '@esbuild/darwin-arm64@0.19.12':
    '@esbuild/darwin-arm64': private
  '@types/triple-beam@1.3.5':
    '@types/triple-beam': private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  buffer-from@1.1.2:
    buffer-from: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  color-convert@1.9.3:
    color-convert: private
  color-name@1.1.3:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@3.2.1:
    color: private
  colorspace@1.1.4:
    colorspace: private
  combined-stream@1.0.8:
    combined-stream: private
  debug@4.4.1:
    debug: private
  delayed-stream@1.0.0:
    delayed-stream: private
  dunder-proto@1.0.1:
    dunder-proto: private
  enabled@2.0.0:
    enabled: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild-register@3.6.0(esbuild@0.19.12):
    esbuild-register: private
  esbuild@0.19.12:
    esbuild: private
  esbuild@0.25.9:
    esbuild: private
  fecha@4.2.3:
    fecha: private
  fn.name@1.1.0:
    fn.name: private
  follow-redirects@1.15.11:
    follow-redirects: private
  form-data@4.0.4:
    form-data: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  gopd@1.2.0:
    gopd: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  inherits@2.0.4:
    inherits: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-stream@2.0.1:
    is-stream: private
  kuler@2.0.0:
    kuler: private
  logform@2.7.0:
    logform: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  ms@2.1.3:
    ms: private
  one-time@1.0.0:
    one-time: private
  pg-cloudflare@1.2.7:
    pg-cloudflare: private
  pg-connection-string@2.9.1:
    pg-connection-string: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-pool@3.10.1(pg@8.16.3):
    pg-pool: private
  pg-protocol@1.10.3:
    pg-protocol: private
  pg-types@2.2.0:
    pg-types: private
  pgpass@1.0.5:
    pgpass: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  readable-stream@3.6.2:
    readable-stream: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  split2@4.2.0:
    split2: private
  stack-trace@0.0.10:
    stack-trace: private
  string_decoder@1.3.0:
    string_decoder: private
  text-hex@1.0.0:
    text-hex: private
  triple-beam@1.4.1:
    triple-beam: private
  undici-types@6.21.0:
    undici-types: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@8.3.2:
    uuid: private
  winston-transport@4.9.0:
    winston-transport: private
  xtend@4.0.2:
    xtend: private
ignoredBuilds:
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.14.0
pendingBuilds: []
prunedAt: Tue, 19 Aug 2025 08:47:37 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/aix-ppc64@0.19.12'
  - '@esbuild/aix-ppc64@0.25.9'
  - '@esbuild/android-arm64@0.18.20'
  - '@esbuild/android-arm64@0.19.12'
  - '@esbuild/android-arm64@0.25.9'
  - '@esbuild/android-arm@0.18.20'
  - '@esbuild/android-arm@0.19.12'
  - '@esbuild/android-arm@0.25.9'
  - '@esbuild/android-x64@0.18.20'
  - '@esbuild/android-x64@0.19.12'
  - '@esbuild/android-x64@0.25.9'
  - '@esbuild/darwin-x64@0.18.20'
  - '@esbuild/darwin-x64@0.19.12'
  - '@esbuild/darwin-x64@0.25.9'
  - '@esbuild/freebsd-arm64@0.18.20'
  - '@esbuild/freebsd-arm64@0.19.12'
  - '@esbuild/freebsd-arm64@0.25.9'
  - '@esbuild/freebsd-x64@0.18.20'
  - '@esbuild/freebsd-x64@0.19.12'
  - '@esbuild/freebsd-x64@0.25.9'
  - '@esbuild/linux-arm64@0.18.20'
  - '@esbuild/linux-arm64@0.19.12'
  - '@esbuild/linux-arm64@0.25.9'
  - '@esbuild/linux-arm@0.18.20'
  - '@esbuild/linux-arm@0.19.12'
  - '@esbuild/linux-arm@0.25.9'
  - '@esbuild/linux-ia32@0.18.20'
  - '@esbuild/linux-ia32@0.19.12'
  - '@esbuild/linux-ia32@0.25.9'
  - '@esbuild/linux-loong64@0.18.20'
  - '@esbuild/linux-loong64@0.19.12'
  - '@esbuild/linux-loong64@0.25.9'
  - '@esbuild/linux-mips64el@0.18.20'
  - '@esbuild/linux-mips64el@0.19.12'
  - '@esbuild/linux-mips64el@0.25.9'
  - '@esbuild/linux-ppc64@0.18.20'
  - '@esbuild/linux-ppc64@0.19.12'
  - '@esbuild/linux-ppc64@0.25.9'
  - '@esbuild/linux-riscv64@0.18.20'
  - '@esbuild/linux-riscv64@0.19.12'
  - '@esbuild/linux-riscv64@0.25.9'
  - '@esbuild/linux-s390x@0.18.20'
  - '@esbuild/linux-s390x@0.19.12'
  - '@esbuild/linux-s390x@0.25.9'
  - '@esbuild/linux-x64@0.18.20'
  - '@esbuild/linux-x64@0.19.12'
  - '@esbuild/linux-x64@0.25.9'
  - '@esbuild/netbsd-arm64@0.25.9'
  - '@esbuild/netbsd-x64@0.18.20'
  - '@esbuild/netbsd-x64@0.19.12'
  - '@esbuild/netbsd-x64@0.25.9'
  - '@esbuild/openbsd-arm64@0.25.9'
  - '@esbuild/openbsd-x64@0.18.20'
  - '@esbuild/openbsd-x64@0.19.12'
  - '@esbuild/openbsd-x64@0.25.9'
  - '@esbuild/openharmony-arm64@0.25.9'
  - '@esbuild/sunos-x64@0.18.20'
  - '@esbuild/sunos-x64@0.19.12'
  - '@esbuild/sunos-x64@0.25.9'
  - '@esbuild/win32-arm64@0.18.20'
  - '@esbuild/win32-arm64@0.19.12'
  - '@esbuild/win32-arm64@0.25.9'
  - '@esbuild/win32-ia32@0.18.20'
  - '@esbuild/win32-ia32@0.19.12'
  - '@esbuild/win32-ia32@0.25.9'
  - '@esbuild/win32-x64@0.18.20'
  - '@esbuild/win32-x64@0.19.12'
  - '@esbuild/win32-x64@0.25.9'
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
