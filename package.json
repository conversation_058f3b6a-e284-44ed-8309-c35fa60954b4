{"name": "stock-ifind", "version": "1.0.0", "description": "Stock data fetcher using iFinD HTTP API with PostgreSQL storage", "main": "dist/app.js", "scripts": {"dev": "tsx src/app.ts", "build": "tsc", "start": "node dist/app.js", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:push": "drizzle-kit push", "fetch:history": "tsx src/scripts/fetch-history.ts", "init:db": "tsx src/scripts/init-database.ts"}, "keywords": ["stock", "ifind", "postgresql", "drizzle", "typescript"], "author": "", "license": "MIT", "dependencies": {"drizzle-orm": "^0.33.0", "pg": "^8.12.0", "axios": "^1.7.7", "dotenv": "^16.4.5", "winston": "^3.14.2", "node-cron": "^3.0.3", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^22.5.4", "@types/pg": "^8.11.8", "@types/node-cron": "^3.0.11", "drizzle-kit": "^0.24.2", "tsx": "^4.19.0", "typescript": "^5.6.2"}, "packageManager": "pnpm@10.14.0+sha512.ad27a79641b49c3e481a16a805baa71817a04bbe06a38d17e60e2eaee83f6a146c6a688125f5792e48dd5ba30e7da52a5cda4c3992b9ccf333f9ce223af84748"}